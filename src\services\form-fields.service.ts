import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  FormFields,
  FormFieldsDocument,
} from '../entities/mongodb/form-fields.entity';
import {
  FormValidationSchema,
  FormValidationSchemaDocument,
} from '../entities/mongodb/form-validation-schema.entity';
import { GROUP } from '../util/interfaces/forms.interface';
import { generateUUID } from '../util';

@Injectable()
export class FormFieldsService {
  constructor(
    @InjectModel(FormFields.name)
    private formFieldsModel: Model<FormFieldsDocument>,
    @InjectModel(FormValidationSchema.name)
    private formValidationModel: Model<FormValidationSchemaDocument>,
  ) {}

  /**
   * Save form fields in separate documents to avoid 16MB limit
   * @param form_id string
   * @param fields Record<string, GROUP>
   * @param validationSchema any
   */
  async saveFormFields(
    form_id: string,
    fields: Record<string, GROUP>,
    validationSchema: any,
  ): Promise<void> {
    // Delete existing fields for this form
    await this.formFieldsModel.deleteMany({ form_id });
    await this.formValidationModel.deleteMany({ form_id });

    // Save each section as a separate document
    const fieldPromises = Object.entries(fields).map(([sectionKey, sectionData]) => {
      return this.formFieldsModel.create({
        form_fields_id: generateUUID(),
        form_id,
        section_key: sectionKey,
        section_name: sectionData.group_title,
        section_index: sectionData.group_index,
        section_data: sectionData,
      });
    });

    // Save validation schema
    const validationPromise = this.formValidationModel.create({
      form_validation_id: generateUUID(),
      form_id,
      validation_schema: validationSchema,
    });

    await Promise.all([...fieldPromises, validationPromise]);
  }

  /**
   * Get form fields by form_id
   * @param form_id string
   * @returns Promise<Record<string, GROUP>>
   */
  async getFormFields(form_id: string): Promise<Record<string, GROUP>> {
    const fieldDocuments = await this.formFieldsModel
      .find({ form_id, deleted_at: null })
      .sort({ section_index: 1 })
      .exec();

    const fields: Record<string, GROUP> = {};
    fieldDocuments.forEach((doc) => {
      fields[doc.section_key] = doc.section_data;
    });

    return fields;
  }

  /**
   * Get validation schema by form_id
   * @param form_id string
   * @returns Promise<any>
   */
  async getValidationSchema(form_id: string): Promise<any> {
    const validationDoc = await this.formValidationModel
      .findOne({ form_id, deleted_at: null })
      .exec();

    return validationDoc?.validation_schema || {};
  }

  /**
   * Update specific section fields
   * @param form_id string
   * @param section_key string
   * @param section_data GROUP
   */
  async updateSectionFields(
    form_id: string,
    section_key: string,
    section_data: GROUP,
  ): Promise<void> {
    await this.formFieldsModel.findOneAndUpdate(
      { form_id, section_key },
      {
        section_name: section_data.group_title,
        section_index: section_data.group_index,
        section_data: section_data,
      },
      { upsert: true },
    );
  }

  /**
   * Delete form fields and validation schema
   * @param form_id string
   */
  async deleteFormFields(form_id: string): Promise<void> {
    await Promise.all([
      this.formFieldsModel.updateMany(
        { form_id },
        { deleted_at: new Date() },
      ),
      this.formValidationModel.updateMany(
        { form_id },
        { deleted_at: new Date() },
      ),
    ]);
  }

  /**
   * Get form fields with pagination for large forms
   * @param form_id string
   * @param page number
   * @param limit number
   */
  async getFormFieldsPaginated(
    form_id: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    fields: Record<string, GROUP>;
    total: number;
    page: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;
    
    const [fieldDocuments, total] = await Promise.all([
      this.formFieldsModel
        .find({ form_id, deleted_at: null })
        .sort({ section_index: 1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.formFieldsModel.countDocuments({ form_id, deleted_at: null }),
    ]);

    const fields: Record<string, GROUP> = {};
    fieldDocuments.forEach((doc) => {
      fields[doc.section_key] = doc.section_data;
    });

    return {
      fields,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }
}
