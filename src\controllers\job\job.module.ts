import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import <PERSON><PERSON>ontroller from './job.controller';
import { JobService } from 'src/services/job.service';
import { Job } from 'src/entities/job.entity';
import { Organization } from 'src/entities/organization.entity';
import { OrganizationService } from 'src/services/organization.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Job, Organization], 'mysql'),
  ],
  exports: [JobService],
  providers: [JobService, OrganizationService],
  controllers: [JobController],
})
export class JobModule {}
