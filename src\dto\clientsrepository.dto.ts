import { IsNotEmpty, IsMobilePhone, IsOptional } from 'class-validator';

export class CreateClientRepositoryDto {
  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @IsOptional()
  @IsNotEmpty()
  mobile_number?: string;

  @IsOptional()
  @IsNotEmpty()
  address?: string;

  @IsOptional()
  e_sign_forms?: any;

  @IsOptional()
  storage_folder_id?: string;

  @IsOptional()
  storage_folder_name?: string;

  @IsOptional()
  storage_location?: string;

  @IsOptional()
  zoho_lead_id?: string;
}
