import { Type } from 'class-transformer';
import {
  IsEnum,
  IsArray,
  IsObject,
  IsNumber,
  IsBoolean,
  ValidateIf,
  IsOptional,
  IsNotEmpty,
  ArrayNotEmpty,
  ValidateNested,
  IsNotEmptyObject,
  IsString,
} from 'class-validator';
import {
  ATTRIBUTES,
  CONDITIONAL_VALIDATION,
  CUSTOM_VALIDATION,
  FIELD,
  FieldDependency,
  FieldInformation,
  GROUP,
  INPUTS,
  INPUT_TYPES,
  OPTIONS,
  QUIZ_FIELD,
  QUIZ_INPUTS,
  QUIZ_INPUT_TYPES,
  TOTAL_SELECTIONS,
  VALIDATION_SCHEMA,
} from 'src/util/interfaces/forms.interface';
import {
  SECTION_VIEW_TYPE,
  TABLET_LAYOUT,
} from 'src/util/interfaces/theme.interface';
import { AppsExists } from 'src/util/validations/apps.validation';
import {
  AutoFillKeysExists,
  ConditionalValidationKeysExists,
  CustomValidationKeysExists,
  FieldExists,
  FieldIdExists,
  FormSectionExists,
  FormsExists,
  HasSubFormsExists,
  SourceFieldIdExists,
  SourceFormIdExists,
  SourceSectionKeyExists,
} from 'src/util/validations/forms.validations';

class Attributes implements ATTRIBUTES {
  @IsNotEmpty()
  attr_type: string;
  @IsNotEmpty()
  attr_value: string;
}

class Options implements OPTIONS {
  @IsNotEmpty()
  value: string;
  @IsNotEmpty()
  label: string;
}

class CustomValidation implements CUSTOM_VALIDATION {
  @IsOptional()
  maxFiles: number;
  @IsOptional()
  maxFileSize: number;
  @IsOptional()
  fileFormat: string;
  @IsOptional()
  startDate: string;
  @IsOptional()
  endDate: string;
  @IsOptional()
  startTime: string;
  @IsOptional()
  endTime: string;
  @IsOptional()
  startDateTime: string;
  @IsOptional()
  endDateTime: string;
  @IsOptional()
  minValue: string;
  @IsOptional()
  maxValue: string;
  @IsOptional()
  country: string;
  @IsOptional()
  minLength: string;
  @IsOptional()
  maxLength: string;
  @IsOptional()
  inputFormat: string;
}

class ConditionalValidation implements CONDITIONAL_VALIDATION {
  @IsOptional()
  selectedFormId: string;
  @IsOptional()
  selectedGroupKey: string;
  @IsOptional()
  @FieldIdExists()
  selectedInputId: string;
  @IsOptional()
  isDisplayed: string;
  @IsOptional()
  isConditionsRequired: string;
  @IsOptional()
  dateComparison: string;
  @IsOptional()
  selectedDate: string;
  @IsOptional()
  selectedOption: string;
}

class ValidationSchema implements VALIDATION_SCHEMA {
  @IsOptional()
  @IsBoolean()
  required: boolean;
  @IsBoolean()
  @IsOptional()
  unique: boolean;
  @IsObject()
  @IsOptional()
  @CustomValidationKeysExists()
  custom_validation: CustomValidation;
  @IsObject()
  @IsOptional()
  @ConditionalValidationKeysExists()
  conditional_validation: ConditionalValidation;
}

export class CustomValidationBody {
  @IsNotEmpty()
  @FieldIdExists()
  field_id: string;
  @IsNotEmpty()
  group_key: string;
  @IsOptional()
  @IsObject()
  @Type(() => ValidationSchema)
  @ValidateNested({ each: true })
  validation_schema: ValidationSchema;
}
class AutoFill implements FieldInformation {
  @IsOptional()
  enabled?: boolean;
  @IsOptional()
  @SourceFormIdExists()
  source_form_id?: string;
  @IsOptional()
  source_form_name?: string;
  @IsOptional()
  source_section_name: string;
  @IsOptional()
  @SourceSectionKeyExists()
  source_section_key: string;
  @IsOptional()
  source_field_name: string;
  @IsOptional()
  @SourceFieldIdExists()
  source_field_id: string;
}
class Field implements FIELD {
  @IsNotEmpty()
  name: string;
  @IsOptional()
  description: string;
  @IsOptional()
  description_status: boolean;
  @IsNotEmpty()
  @IsEnum(INPUTS)
  type: INPUTS;
  @ValidateIf((o) => o.type !== INPUTS.paragraph)
  @IsNotEmpty()
  label: string;
  @IsOptional()
  @IsArray()
  @Type(() => Attributes)
  @ValidateNested({ each: true })
  attributes: Attributes[];
  @IsOptional()
  input_class: string;
  @IsOptional()
  input_id: string;
  @IsOptional()
  value: string | any;
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => ValidationSchema)
  validation_schema: ValidationSchema;
  @ValidateIf((o) => o.type === INPUTS.input)
  @IsEnum(INPUT_TYPES)
  input_type: INPUT_TYPES;
  @ValidateIf(
    (o) =>
      o.type === INPUTS.select ||
      (o.input_type === INPUT_TYPES.checkbox && o.type === INPUTS.input) ||
      (o.input_type === INPUT_TYPES.radio && o.type === INPUTS.input),
  )
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Options)
  @ValidateNested({ each: true })
  options: OPTIONS[];
  @IsNotEmpty()
  @FieldExists()
  original_field_id: string;
  @IsOptional()
  field_id: string;
  @IsOptional()
  field_index: number;
  @IsOptional()
  @IsBoolean()
  is_iterative_or_not?: boolean;
  @IsOptional()
  @IsNumber()
  iteration_min_length?: number;
  @IsOptional()
  @IsNumber()
  iteration_max_length?: number;
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => AutoFill)
  auto_fill?: AutoFill;
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => AutoFill)
  auto_fill_dependencies?: AutoFill[];
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => Dependencies)
  dependencies?: Dependencies[];
}

class TotalSelections implements TOTAL_SELECTIONS {
  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsNumber()
  value?: number;
}

class QuizField implements QUIZ_FIELD {
  @IsBoolean()
  @IsNotEmpty()
  is_quiz_field?: boolean;

  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  description_status: boolean;

  @IsNotEmpty()
  @IsEnum(QUIZ_INPUTS)
  type: QUIZ_INPUTS;

  @IsNotEmpty()
  label: string;

  @IsOptional()
  @IsArray()
  @Type(() => Attributes)
  @ValidateNested({ each: true })
  attributes: Attributes[];

  @IsOptional()
  input_class: string;

  @IsOptional()
  input_id: string;

  @IsOptional()
  value: string | any;

  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => ValidationSchema)
  validation_schema: ValidationSchema;

  @ValidateIf((o) => o.type === QUIZ_INPUTS.input)
  @IsEnum(QUIZ_INPUT_TYPES)
  input_type: QUIZ_INPUT_TYPES;

  @ValidateIf(
    (o) =>
      o.type === QUIZ_INPUTS.select ||
      (o.input_type === QUIZ_INPUT_TYPES.checkbox &&
        o.type === QUIZ_INPUTS.input) ||
      (o.input_type === QUIZ_INPUT_TYPES.radio && o.type === QUIZ_INPUTS.input),
  )
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Options)
  @ValidateNested({ each: true })
  options: OPTIONS[];

  @IsNotEmpty()
  @FieldExists()
  original_field_id: string;

  @IsOptional()
  field_id: string;

  @IsOptional()
  field_index: number;

  @IsOptional()
  @IsNumber()
  points: number;

  @IsOptional()
  @IsString()
  checkbox_answer_type: string;

  @IsOptional()
  @IsString()
  checkbox_answer_limit: string;

  @IsOptional()
  @IsBoolean()
  is_negative_marking?: boolean;

  @IsOptional()
  @IsNumber()
  negative_marking_points?: number;
}

class Columns implements GROUP {
  @IsNotEmpty()
  group_title: string;

  @IsOptional()
  group_key: string;

  @IsOptional()
  group_index: number;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Field)
  fields: Field[];
}

export class FormsRepoDto {
  @IsNotEmpty()
  name: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Columns)
  groups: Columns[];

  staus: number;

  @IsNotEmpty()
  @AppsExists()
  app_id: string;

  @IsOptional()
  description: string;

  @IsOptional()
  @IsNumber()
  order_position: number;

  // @IsNotEmpty()
  // @OrganizationExists()
  // organization: string;
}

export class FormSectionRepoDto {
  @IsNotEmpty()
  @FormSectionExists()
  name: string;

  @IsOptional()
  description: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Field)
  fields: Field[];

  @IsOptional()
  status: number;
}

export class UpdateFormSectionRepoDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  description: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Field)
  fields: Field[];

  @IsOptional()
  status: number;
}

export class UpdateFormsRepoDto {
  @IsNotEmpty()
  name: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => Columns)
  groups: Columns[];

  staus: number;

  @IsOptional()
  @AppsExists()
  app_id: string;

  @IsOptional()
  @IsNumber()
  order_position: number;
  // @IsNotEmpty()
  // @OrganizationExists()
  // organization: string;
}

export class FormFieldsDto {
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsBoolean()
  is_quiz_field: boolean;

  @IsNotEmpty()
  skelton: FIELD | QUIZ_FIELD;
}

export class AutoCreateFormsRepoDto {
  @IsOptional()
  @AppsExists()
  app_id: string;

  @IsOptional()
  name: string;

  @IsOptional()
  description: string;

  @IsOptional()
  @IsBoolean()
  has_sub_forms: boolean;

  @IsBoolean()
  @IsOptional()
  is_sub_form: boolean;

  @ValidateIf((o) => o.is_sub_form)
  @IsNotEmpty()
  @HasSubFormsExists()
  main_form_id: string;

  @IsOptional()
  @IsBoolean()
  is_quiz_form: boolean;
}

export class AutoFieldDto {
  @IsNotEmpty()
  @ValidateIf((o) => !o.group_key)
  group_title: string;

  @IsNotEmpty()
  @ValidateIf((o) => !o.group_title)
  group_key: string;

  @IsOptional()
  group_description: string;

  @IsObject()
  @IsNotEmptyObject()
  @ValidateNested({ each: true })
  @Type(() => Field || QuizField)
  field: Field | QuizField;
}

class Group {
  @IsNotEmpty()
  key: string;
  @IsNotEmpty()
  title: string;
  @IsOptional()
  index: number;
  @IsOptional()
  description: number;
  @IsBoolean()
  @IsOptional()
  is_iterative_or_not: boolean;
  @IsNumber()
  @IsOptional()
  iteration_min_length: boolean;
  @IsNumber()
  @IsOptional()
  iteration_max_length: boolean;
}

export class FormDetails {
  @IsOptional()
  name: string;
  @IsOptional()
  status: boolean;
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => Group)
  group: Group;
  @IsOptional()
  @IsEnum(SECTION_VIEW_TYPE)
  sectionViewType: SECTION_VIEW_TYPE;
  @IsOptional()
  @IsEnum(TABLET_LAYOUT)
  tabletLayout: TABLET_LAYOUT;
  @IsOptional()
  @IsNumber()
  order_position: number;
}

export class UpdateField implements FIELD {
  @IsOptional()
  name: string;
  @IsOptional()
  description: string;
  @IsOptional()
  description_status: boolean;
  @IsOptional()
  type: string;
  @IsOptional()
  label: string;
  @IsOptional()
  input_class: string;
  @IsOptional()
  input_id: string;
  @IsOptional()
  value: string;
  @IsOptional()
  @IsObject()
  @Type(() => ValidationSchema)
  @ValidateNested({ each: true })
  validation_schema: ValidationSchema;
  @ValidateIf((o) => o.type === INPUTS.input)
  @IsEnum(INPUT_TYPES)
  input_type: INPUT_TYPES;
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => Options)
  @ValidateNested({ each: true })
  @ValidateIf(
    (o) =>
      o.type === INPUTS.select ||
      (o.input_type === INPUT_TYPES.checkbox && o.type === INPUTS.input) ||
      (o.input_type === INPUT_TYPES.radio && o.type === INPUTS.input),
  )
  options: Options[];
  @IsOptional()
  @IsArray()
  @Type(() => Attributes)
  @ValidateNested({ each: true })
  attributes: Attributes[];
  @IsNotEmpty()
  field_id: string;
  @IsOptional()
  @FieldExists()
  original_field_id: string;
  @IsNotEmpty()
  group_key: string;
  @IsOptional()
  field_index: number;
  @IsOptional()
  @IsBoolean()
  is_iterative_or_not?: boolean;
  @IsOptional()
  @IsNumber()
  iteration_min_length?: number;
  @IsOptional()
  @IsNumber()
  iteration_max_length?: number;
  @IsOptional()
  @IsObject()
  @AutoFillKeysExists()
  @ValidateNested({ each: true })
  @Type(() => AutoFill)
  auto_fill?: AutoFill;
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => AutoFill)
  auto_fill_dependencies?: AutoFill[];
  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => Dependencies)
  dependencies?: Dependencies[];
}

export class Dependencies implements FieldDependency {
  @IsOptional()
  selectedFormId: string;
  @IsOptional()
  selectedGroupKey: string;
  @IsOptional()
  selectedInputId: string;
}

export class DeleteFieldDto {
  @IsOptional()
  field_id: string;
  @IsNotEmpty()
  group_key: string;
}

class GroupIndex {
  @IsNotEmpty()
  group_key: string;
  @IsNotEmpty()
  group_index: number;
}

class FieldIndex {
  @IsNotEmpty()
  field_id: string;
  @IsNotEmpty()
  field_index: number;
}

export class UpdateIndex {
  @IsNotEmpty()
  group_key: string;

  @ValidateIf((o) => !o.fields || o.fields?.length === 0)
  @IsNotEmpty()
  group_index: number;

  @ArrayNotEmpty()
  @ValidateIf((o) => !o.group_index)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FieldIndex)
  fields: FieldIndex[];

  @IsOptional()
  @IsString()
  update_type: string;

  @ArrayNotEmpty()
  @ValidateIf((o) => o.update_type !== undefined || null)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FieldIndex)
  groups: FieldIndex[];
}

export class UpdateGroupIndex {
  @ArrayNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GroupIndex)
  groups: GroupIndex[];
}

export class CopyFieldDto {
  @IsNotEmpty()
  group_key: string;
  @IsNotEmpty()
  field_id: string;
}

export class AddSectionDto {
  @IsNotEmpty()
  section_id: string;

  @IsNotEmpty()
  group_index: number;
}

export class RemoveSectionDto {
  @IsNotEmpty()
  section_id: string;
}

export class UpdateFormsIndex {
  @IsArray()
  @IsNotEmpty({ each: true })
  forms: FormIndex[];
}

export class FormIndex {
  @IsNotEmpty()
  form_id: string;

  @IsNotEmpty()
  form_index: number;
}
