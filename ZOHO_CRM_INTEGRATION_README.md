# Zoho CRM Integration for Client Repository

## Overview
This document describes the implementation of Zoho CRM integration with the Client Repository system. When new clients are added to the system, leads are automatically created in Zoho CRM if the integration is configured and authenticated.

## Features

### Automatic Lead Creation
- **Auto-sync on Client Creation**: When a new client is created, a lead is automatically created in Zoho CRM
- **Error Handling**: Client creation succeeds even if Zoho CRM sync fails
- **Lead ID Storage**: Zoho lead ID is stored in the client record for reference

### Manual Sync Operations
- **Individual Client Sync**: Sync specific clients to Zoho CRM
- **Bulk Sync**: Sync all unsynced clients to Zoho CRM
- **Duplicate Prevention**: Prevents creating duplicate leads for already synced clients

### Configuration Management
- **Authentication Check**: Validates Zoho CRM configuration before attempting sync
- **Token Refresh**: Automatically refreshes expired access tokens
- **Error Recovery**: Retries operations after token refresh

## Prerequisites

### Zoho CRM Configuration
1. **Zoho CRM Account**: Active Zoho CRM subscription
2. **OAuth Application**: Create OAuth application in Zoho Developer Console
3. **API Permissions**: Required scopes: `ZohoCRM.modules.ALL`, `ZohoCRM.settings.ALL`
4. **Configuration Setup**: Configure CRM integration in the application

### Required Configuration Fields
- `client_id`: Zoho OAuth client ID
- `client_secret`: Zoho OAuth client secret
- `redirect_uri`: OAuth redirect URI
- `access_token`: OAuth access token (obtained during authentication)
- `refresh_token`: OAuth refresh token
- `api_url`: Zoho accounts server URL (e.g., https://accounts.zoho.com)

## API Endpoints

### Automatic Integration
The integration works automatically when creating clients through the existing endpoint:

```http
POST /clients-repository
```

### Manual Sync Endpoints

#### Sync Individual Client
```http
POST /clients-repository/:client_id/sync-zoho-crm
```

**Response:**
```json
{
  "status": true,
  "message": "Client synced successfully to Zoho CRM",
  "data": {
    "zoho_lead_id": "4876876000000123456",
    "data": {
      "code": "SUCCESS",
      "details": {
        "id": "4876876000000123456"
      }
    }
  }
}
```

#### Bulk Sync All Clients
```http
POST /clients-repository/bulk-sync-zoho-crm
```

**Response:**
```json
{
  "status": true,
  "message": "Bulk sync completed. 5 clients synced, 1 failed.",
  "data": {
    "total": 6,
    "synced": 5,
    "failed": 1,
    "errors": [
      {
        "client_id": "client-uuid-123",
        "error": "Invalid phone number format"
      }
    ]
  }
}
```

## Lead Data Mapping

### Client to Zoho CRM Lead Mapping
| Client Field | Zoho CRM Field | Description |
|--------------|----------------|-------------|
| `name` | `Company` | Client/Company name |
| `name` | `Last_Name` | Contact last name |
| `mobile_number` | `Phone` | Phone number |
| `address` | `Street` | Street address |
| `client_id` | `Description` | Reference to Kleza client ID |
| - | `Lead_Source` | Set to "Kleza Forms" |
| - | `Lead_Status` | Set to "Not Contacted" |

### Additional Fields Stored
- `zoho_lead_id`: Stored in client record for reference
- `created_at`: Timestamp of lead creation

## Error Handling

### Client Creation Flow
1. **Client Creation**: Always succeeds regardless of Zoho CRM status
2. **CRM Check**: Validates if Zoho CRM is configured
3. **Lead Creation**: Attempts to create lead in Zoho CRM
4. **Error Logging**: Logs errors without failing client creation
5. **ID Storage**: Stores Zoho lead ID if successful

### Common Error Scenarios
- **CRM Not Configured**: Silently skipped during auto-sync
- **Authentication Failed**: Attempts token refresh
- **API Rate Limits**: Logged as warnings
- **Invalid Data**: Specific field validation errors

## Configuration Service Methods

### New Methods Added

#### `createZohoCrmLead(organization_id, leadData)`
Creates a lead in Zoho CRM with automatic token refresh handling.

#### `isZohoCrmConfigured(organization_id)`
Checks if Zoho CRM is properly configured and authenticated.

#### `refreshZohoCrmToken(organization_id)`
Refreshes the Zoho CRM access token using the refresh token.

## Database Schema Changes

### ClientsRepository Entity
```typescript
// New field added
@Prop({ type: SchemaTypes.String, required: false })
zoho_lead_id: string;
```

### DTO Updates
```typescript
// New optional field
@IsOptional()
zoho_lead_id?: string;
```

## Usage Examples

### Automatic Sync (Default Behavior)
```javascript
// When creating a client, Zoho CRM lead is automatically created
const client = await clientsRepositoryService.create(clientData, user, appCode);
// Client is created and Zoho lead ID is stored if CRM is configured
```

### Manual Sync
```javascript
// Sync specific client
const result = await clientsRepositoryService.syncClientToZohoCrm(
  'client-uuid-123',
  'org-uuid-456'
);

// Bulk sync all unsynced clients
const bulkResult = await clientsRepositoryService.bulkSyncClientsToZohoCrm(
  'org-uuid-456'
);
```

## Security Considerations

1. **Token Security**: Access tokens are stored securely in the database
2. **Organization Isolation**: Each organization has separate CRM configuration
3. **Authentication**: All endpoints require JWT authentication
4. **Error Information**: Sensitive error details are not exposed to clients

## Monitoring and Logging

### Success Logging
- Lead creation success with Zoho lead ID
- Bulk sync completion statistics

### Error Logging
- CRM configuration issues
- Authentication failures
- API communication errors
- Data validation errors

## Best Practices

1. **Configure Webhooks**: Set up Zoho CRM webhooks for bidirectional sync
2. **Monitor Rate Limits**: Zoho CRM has API rate limits
3. **Regular Token Refresh**: Tokens expire and need periodic refresh
4. **Data Validation**: Ensure client data meets Zoho CRM requirements
5. **Error Monitoring**: Monitor logs for sync failures

## Files Modified/Created

### Modified Files
- `src/services/configurations.service.ts` - Added Zoho CRM lead creation methods
- `src/services/clientsrepository.service.ts` - Added CRM integration to client creation
- `src/controllers/clientsrepository/clientsrepository.controller.ts` - Added sync endpoints
- `src/controllers/clientsrepository/clientsrepository.module.ts` - Added ConfigurationsService
- `src/entities/mongodb/clientsrepository.entity.ts` - Added zoho_lead_id field
- `src/dto/clientsrepository.dto.ts` - Added zoho_lead_id field

### Key Benefits
1. **Seamless Integration**: Automatic lead creation without user intervention
2. **Data Consistency**: Client data automatically synced to CRM
3. **Error Resilience**: Client creation never fails due to CRM issues
4. **Manual Control**: Ability to sync existing clients manually
5. **Audit Trail**: Complete tracking of sync status and errors
