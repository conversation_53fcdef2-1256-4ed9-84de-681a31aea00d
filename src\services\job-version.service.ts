import {
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  FindOptionsSelect,
  FindOptionsWhere,
  Repository,
} from 'typeorm';
import { JobVersion } from 'src/entities/job-version.entity';
import { Job } from 'src/entities/job.entity';
import { Organization } from 'src/entities/organization.entity';
import { generateUUID } from 'src/util';

@Injectable()
export class JobVersionService {
  constructor(
    @InjectRepository(JobVersion, 'mysql')
    private jobVersionRepository: Repository<JobVersion>,
  ) {}

  public select: FindOptionsSelect<JobVersion> = {
    id: false,
    job_version_id: true,
    version: true,
    title: true,
    department: true,
    description: true,
    requirements: true,
    salary_range: true,
    employment_type: true,
    location: true,
    status: true,
    application_start_date: true,
    application_end_date: true,
    change_reason: true,
    is_current_version: true,
    created_at: true,
    updated_at: false,
    deleted_at: false,
  };

  /**
   * Create Job Version
   * @param job Job
   * @param changeReason string
   * @returns Promise<JobVersion>
   */
  async createVersion(
    job: Job,
    changeReason?: string,
  ): Promise<JobVersion> {
    try {
      // Mark all previous versions as not current
      await this.jobVersionRepository.update(
        { job: { id: job.id } },
        { is_current_version: false },
      );

      const jobVersion = await this.jobVersionRepository.create({
        job_version_id: generateUUID(),
        job: { id: job.id },
        version: job.version,
        title: job.title,
        department: job.department,
        description: job.description,
        requirements: job.requirements,
        salary_range: job.salary_range,
        employment_type: job.employment_type,
        location: job.location,
        status: job.status,
        application_start_date: job.application_start_date,
        application_end_date: job.application_end_date,
        organization: job.organization,
        change_reason: changeReason,
        is_current_version: true,
      });

      return await this.jobVersionRepository.save(jobVersion);
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find Job Versions
   * @param findBy Object "{column: value}"
   * @param withDeleted boolean
   * @param select FindOptionsSelect<JobVersion>
   * @returns Promise<JobVersion[]>
   */
  async find(
    findBy: FindOptionsWhere<JobVersion>,
    withDeleted = false,
    select = this.select,
  ): Promise<JobVersion[]> {
    try {
      return await this.jobVersionRepository.find({
        where: findBy,
        withDeleted,
        select,
        relations: {
          job: true,
          organization: true,
        },
        order: {
          version: 'DESC',
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find One Job Version
   * @param findBy Object "{column: value}"
   * @param withDeleted boolean
   * @param select FindOptionsSelect<JobVersion>
   * @returns Promise<JobVersion>
   */
  async findOne(
    findBy: FindOptionsWhere<JobVersion>,
    withDeleted = false,
    select = this.select,
  ): Promise<JobVersion> {
    try {
      return await this.jobVersionRepository.findOne({
        where: findBy,
        withDeleted,
        select,
        relations: {
          job: true,
          organization: true,
        },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Current Version of a Job
   * @param job_id string
   * @returns Promise<JobVersion>
   */
  async getCurrentVersion(job_id: string): Promise<JobVersion> {
    try {
      return await this.findOne({
        job: { job_id },
        is_current_version: true,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Job History
   * @param job_id string
   * @returns Promise<JobVersion[]>
   */
  async getJobHistory(job_id: string): Promise<JobVersion[]> {
    try {
      return await this.find({
        job: { job_id },
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
