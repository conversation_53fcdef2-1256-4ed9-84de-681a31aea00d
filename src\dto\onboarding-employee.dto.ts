import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>E<PERSON>,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsDateString,
} from 'class-validator';
import { EMPLOYEE_STATUS } from 'src/entities/onboarding-employee.entity';

export class OnboardingEmployeeDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  mobile_number: string;

  @IsNotEmpty()
  password: string;

  @IsOptional()
  @IsString()
  @IsEnum(EMPLOYEE_STATUS)
  status: EMPLOYEE_STATUS;

  @IsOptional()
  @IsString()
  job_id: string;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;
}

export class UpdateOnboardingEmployeeDto {
  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  mobile_number: string;

  @IsOptional()
  password: string;

  @IsOptional()
  @IsString()
  @IsEnum(EMPLOYEE_STATUS)
  status: EMPLOYEE_STATUS;

  @IsArray()
  @IsOptional()
  appIds: [String];

  @IsOptional()
  @IsString()
  scheduled_interview_time: string;

  @IsOptional()
  @IsString()
  scheduled_interview_date: string;

  @IsOptional()
  bg_verification_document: any;

  @IsOptional()
  @IsString()
  job_id: string;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;
}
