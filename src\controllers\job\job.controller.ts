import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  Res,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthGuard } from '@nestjs/passport';
import { OrganizationAuth } from 'src/guards/organization.guard';
import { JobService } from 'src/services/job.service';
import { OrganizationService } from 'src/services/organization.service';
import { CreateJobDto, UpdateJobDto } from 'src/dto/job.dto';
import { FindOptionsWhere } from 'typeorm';
import { Job, JOB_STATUS } from 'src/entities/job.entity';

@Controller('job')
export default class JobController {
  constructor(
    private jobService: JobService,
    private readonly organizationService: OrganizationService,
  ) {}

  /**
   * Create Job
   * @param request Request
   * @param response Response
   * @param createJobDto CreateJobDto
   * @returns Promise<Response>
   */
  @Post('')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async create(
    @Req() request: Request,
    @Res() response: Response,
    @Body() createJobDto: CreateJobDto,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const job = await this.jobService.create(createJobDto, organization);

      return response.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Job created successfully',
        data: job,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Jobs
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async find(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const findBy: FindOptionsWhere<Job> = {
        organization,
      };

      const jobs = await this.jobService.find(findBy);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Jobs retrieved successfully',
        data: jobs,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Active Jobs
   * @param request Request
   * @param response Response
   * @returns Promise<Response>
   */
  @Get('active')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findActive(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const findBy: FindOptionsWhere<Job> = {
        organization,
        status: JOB_STATUS.ACTIVE,
      };

      const jobs = await this.jobService.find(findBy);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Active jobs retrieved successfully',
        data: jobs,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Job by ID
   * @param request Request
   * @param response Response
   * @param job_id string
   * @returns Promise<Response>
   */
  @Get(':job_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async findOne(
    @Req() request: Request,
    @Res() response: Response,
    @Param('job_id') job_id: string,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      const job = await this.jobService.findOne({
        job_id,
        organization,
      });

      if (!job) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Job retrieved successfully',
        data: job,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update Job
   * @param request Request
   * @param response Response
   * @param job_id string
   * @param updateJobDto UpdateJobDto
   * @returns Promise<Response>
   */
  @Put(':job_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async update(
    @Req() request: Request,
    @Res() response: Response,
    @Param('job_id') job_id: string,
    @Body() updateJobDto: UpdateJobDto,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      // Verify job belongs to organization
      const existingJob = await this.jobService.findOne({
        job_id,
        organization,
      });

      if (!existingJob) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      const job = await this.jobService.update(job_id, updateJobDto);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Job updated successfully',
        data: job,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete Job
   * @param request Request
   * @param response Response
   * @param job_id string
   * @returns Promise<Response>
   */
  @Delete(':job_id')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async delete(
    @Req() request: Request,
    @Res() response: Response,
    @Param('job_id') job_id: string,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      // Verify job belongs to organization
      const existingJob = await this.jobService.findOne({
        job_id,
        organization,
      });

      if (!existingJob) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      await this.jobService.delete(job_id);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Job deleted successfully',
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Job History
   * @param request Request
   * @param response Response
   * @param job_id string
   * @returns Promise<Response>
   */
  @Get(':job_id/history')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getJobHistory(
    @Req() request: Request,
    @Res() response: Response,
    @Param('job_id') job_id: string,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      // Verify job belongs to organization
      const existingJob = await this.jobService.findOne({
        job_id,
        organization,
      });

      if (!existingJob) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      const jobHistory = await this.jobService.getJobHistory(job_id);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Job history retrieved successfully',
        data: jobHistory,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Current Job Version
   * @param request Request
   * @param response Response
   * @param job_id string
   * @returns Promise<Response>
   */
  @Get(':job_id/current-version')
  @UsePipes(ValidationPipe)
  @UseGuards(AuthGuard('jwt'), OrganizationAuth)
  async getCurrentJobVersion(
    @Req() request: Request,
    @Res() response: Response,
    @Param('job_id') job_id: string,
  ): Promise<Response> {
    try {
      const organization = await this.organizationService.findOne(
        {
          organization_id: request?.user['organization_id'],
        },
        false,
        { id: true },
      );

      if (!organization) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }

      // Verify job belongs to organization
      const existingJob = await this.jobService.findOne({
        job_id,
        organization,
      });

      if (!existingJob) {
        throw new HttpException('Job not found', HttpStatus.NOT_FOUND);
      }

      const currentVersion = await this.jobService.getCurrentJobVersion(job_id);

      return response.status(HttpStatus.OK).json({
        success: true,
        message: 'Current job version retrieved successfully',
        data: currentVersion,
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
