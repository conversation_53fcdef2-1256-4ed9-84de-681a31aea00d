import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from '@nestjs/common';
import * as _ from 'lodash';
import { ObjectID } from 'mongodb';
import { InjectModel } from '@nestjs/mongoose';
import { Model, QueryOptions } from 'mongoose';
import {
  formatStringToKey,
  generateUUID,
  getCreatedByFromUser,
  isEvenNumber,
  isObjectEmpty,
  sanitizeFileName,
} from 'src/util';
import {
  AutoCreateFormsRepoDto,
  AutoFieldDto,
  CopyFieldDto,
  CustomValidationBody,
  DeleteFieldDto,
  FormDetails,
  FormIndex,
  UpdateField,
  UpdateFormsIndex,
  UpdateGroupIndex,
  UpdateIndex,
} from 'src/dto/formrepository.dto';
import {
  APP_FORMS,
  AUTO_CREATE_FORM,
  FIELD,
  GROUP,
  INPUTS,
} from 'src/util/interfaces/forms.interface';
import { AppsService } from './apps.service';
import { Apps } from 'src/entities/apps.entity';
import { GlobalService } from './global.service';
import {
  FormsRepository,
  FormsRepositoryDocument,
} from 'src/entities/mongodb/formsrepository.entity';
import {
  OrgFormsRepository,
  OrgFormsRepositoryDocument,
} from 'src/entities/mongodb/orgformsrepository.entity';
import {
  FormValuesRepository,
  FormValuesRepositoryDocument,
} from 'src/entities/mongodb/formvaluesrepository.entity';
import {
  ClientsRepository,
  ClientsRepositoryDocument,
} from 'src/entities/mongodb/clientsrepository.entity';
import { OrganizationService } from './organization.service';
import { FormsRepositoryService } from './formsrespository.service';
import { ClientsRepositoryService } from './clientsrepository.service';
import * as moment from 'moment';
import { SendGridService } from 'src/util/sendgrid.service';
import { Configurations } from 'src/entities/configurations.entity';
import { FindOptionsWhere, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ESignRequestLogsRepository,
  ESignRequestLogsRepositoryDocument,
} from 'src/entities/mongodb/e-sign-request-log.entity';
import * as pdf from 'html-pdf-node';
import { ConfigurationsService } from './configurations.service';
import * as fs from 'fs';
import * as path from 'path';
import * as archiver from 'archiver';
import * as mimetype from 'mime-types';
import EncryptionService from './encryption.service';
import {
  PrimaryFormsRepository,
  PrimaryFormsRepositoryDocument,
} from 'src/entities/mongodb/primary-forms-repository.entity';
import {
  AppOrgFormsToggleRepository,
  AppOrgFormsToggleRepositoryDocument,
} from 'src/entities/mongodb/app-org-forms-toggle.entity';
import { OrgAppConfiguration } from 'src/entities/org-app-configuration.entity';
import axios from 'axios';
import { Organization } from 'src/entities/organization.entity';
import { OnboardingEmployeeService } from './onboarding-employee.service';

@Injectable()
export class OrgFormsRepositoryService {
  constructor(
    @InjectModel(OrgFormsRepository.name)
    private OrgFormsRepository: Model<OrgFormsRepositoryDocument>,

    @InjectModel(FormsRepository.name)
    private FormsRepository: Model<FormsRepositoryDocument>,

    @InjectModel(FormValuesRepository.name)
    private formValueRepository: Model<FormValuesRepositoryDocument>,

    @InjectModel(ClientsRepository.name)
    private clientsRepository: Model<ClientsRepositoryDocument>,

    @InjectModel(ESignRequestLogsRepository.name)
    private eSignRequestLogsRepository: Model<ESignRequestLogsRepositoryDocument>,

    @InjectModel(PrimaryFormsRepository.name)
    private primaryFormsRepository: Model<PrimaryFormsRepositoryDocument>,

    @InjectModel(AppOrgFormsToggleRepository.name)
    private appOrgFormsToggleRepository: Model<AppOrgFormsToggleRepositoryDocument>,

    @InjectRepository(Configurations, 'mysql')
    private ConfigurationsRepository: Repository<Configurations>,

    @InjectRepository(OrgAppConfiguration, 'mysql')
    private orgAppConfigurationRepository: Repository<OrgAppConfiguration>,

    private FormsRepositoryService: FormsRepositoryService,

    @Inject(forwardRef(() => OrganizationService))
    private readonly orgService: OrganizationService,

    private readonly globalService: GlobalService,

    private readonly appsService: AppsService,

    private readonly clientsRepositoryService: ClientsRepositoryService,

    private readonly sendGridService: SendGridService,

    private readonly configurationsService: ConfigurationsService,

    private readonly encryptionService: EncryptionService,

    @Inject(forwardRef(() => OnboardingEmployeeService))
    private readonly onboardingEmployeeService: OnboardingEmployeeService,
  ) {}

  public select: QueryOptions<OrgFormsRepository> = {
    _id: false,
    name: true,
    icon: true,
    description: true,
    fields: true,
    status: true,
    is_disable_form_response: true,
    has_multiple_form_responses: true,
    form_id: true,
    app_id: true,
    has_sub_forms: true,
    is_quiz_form: true,
    is_sub_form: true,
    validationschema: true,
    order_position: true,
    sectionViewType: true,
    tabletLayout: true,
    created_at: true,
    updated_at: true,
  };

  /**
   * Insert Multiple Forms at a single time
   * @param data OrgFormsRepository[]
   * @returns Promise<Boolean>
   */
  async insertMany(data: OrgFormsRepository[]): Promise<Boolean> {
    try {
      const savedData = await this.OrgFormsRepository.insertMany(data).catch(
        (error) => {
          throw error;
        },
      );
      return savedData ? true : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the organization forms based on condition
   * @param condition any {key: value}
   * @param expand string[]
   * @returns Promise<OrgFormsRepository[]>
   */
  async find(
    condition: QueryOptions<OrgFormsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<OrgFormsRepository> = this.select,
    expand?: string[],
  ): Promise<OrgFormsRepository[]> {
    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    } else {
      condition['deleted_at'] = { $eq: null };
    }
    let result = this.OrgFormsRepository.find(condition).select(select);
    if (expand) result = result.populate(expand);
    return await result;
  }

  /**
   * Get the organization form based on condition
   * @param condition any {key: value}
   * @param expand string[]
   * @returns Promise<OrgFormsRepository[]>
   */
  async findOne(
    condition: QueryOptions<OrgFormsRepository> = { status: true },
    withDeleted = false,
    select: QueryOptions<OrgFormsRepository> = this.select,
    expand?: any,
  ): Promise<OrgFormsRepository> {
    if ('id' in condition) {
      condition._id = ObjectID(condition.id);
      delete condition.id;
    }
    if (withDeleted) {
      condition['deleted_at'] = { $ne: null };
    }
    let result = this.OrgFormsRepository.findOne(condition).select(select);
    if (expand) result = result.populate(expand);
    return await result;
  }

  /**
   * find by id and update the form
   * @param id string
   * @param data any
   */
  async findByIdAndUpdate(id: string, data: any) {
    await this.OrgFormsRepository.findByIdAndUpdate(id, data);
  }

  async bulkUpdate(
    condition: QueryOptions<OrgFormsRepository>,
    data: any,
  ): Promise<any> {
    return await this.OrgFormsRepository.updateMany(condition, data);
  }

  /**
   * Create multiple forms at a time by original forms id and origanization id
   * @param data string[]
   * @param organization_id  strinf
   * @returns Promise<Boolean>
   */
  async insertFormByFormIds(
    data: string[],
    organization_id: string,
    user?: any,
  ): Promise<Boolean> {
    try {
      let forms = await this.FormsRepositoryService.list(
        {
          form_id: { $in: data },
          status: true,
        },
        false,
        { ...this.FormsRepositoryService.select, _id: true, sub_forms: true },
      );
      const org_forms: OrgFormsRepository[] = await this.find(
        {
          organization: organization_id,
          original_form_id: { $in: forms.map((form: any) => form._id) },
        },
        true,
        { ...this.select, original_form_id: true, _id: true },
      );

      const org_forms_id = org_forms.map((form) =>
        form.original_form_id.toString(),
      );
      forms = forms.filter(
        (form: any) => !org_forms_id.includes(form._id.toString()),
      );
      const deletedForms = org_forms
        .filter((form: OrgFormsRepository) => form.deleted_at !== null)
        .map((form: OrgFormsRepository) => form['_id']);
      await this.OrgFormsRepository.updateMany(
        { _id: { $in: deletedForms } },
        {
          deleted_at: null,
          $addToSet: {
            restored_by: getCreatedByFromUser(user),
          },
        },
      ).catch((error) => {
        throw error;
      });
      const sub_form_ids: { form_id: string; sub_forms: any[] }[] = [];
      const created_by = getCreatedByFromUser(user);
      const savedData = forms.map((form: any) => {
        const _id = form._id;
        delete form._doc._id;
        delete form._doc.form_id;
        const form_id = generateUUID();
        if (form._doc.has_sub_forms) {
          if (form._doc.sub_forms)
            sub_form_ids.push({ form_id, sub_forms: form._doc.sub_forms });
        }
        return {
          ...form._doc,
          form_id,
          sub_forms: [],
          original_form_id: _id,
          organization: organization_id,
          created_by,
          status: true,
        };
      });
      const isInserted = await this.insertMany(savedData);
      if (sub_form_ids.length) {
        for (let i = 0; i < sub_form_ids.length; i++) {
          const sub_form_id = sub_form_ids[i];
          let sub_forms = await this.FormsRepositoryService.list(
            {
              _id: { $in: sub_form_id.sub_forms },
            },
            false,
            {
              ...this.FormsRepositoryService.select,
              _id: true,
              sub_forms: true,
            },
          );
          const existed_sub_forms = await this.find(
            {
              organization: organization_id,
              original_form_id: { $in: sub_forms.map((form: any) => form._id) },
            },
            true,
            { ...this.select, original_form_id: true, _id: true },
          );
          const org_forms_id = existed_sub_forms.map((form) =>
            form.original_form_id.toString(),
          );
          sub_forms = sub_forms.filter(
            (form: any) => !org_forms_id.includes(form._id.toString()),
          );
          const deletedForms = existed_sub_forms
            .filter((form: OrgFormsRepository) => form.deleted_at !== null)
            .map((form: OrgFormsRepository) => form['_id']);
          await this.OrgFormsRepository.updateMany(
            { _id: { $in: deletedForms } },
            {
              deleted_at: null,
              $addToSet: {
                restored_by: getCreatedByFromUser(user),
              },
            },
          ).catch((error) => {
            throw error;
          });
          sub_forms.map(async (form: any) => {
            const _id = form._id;
            delete form._doc._id;
            delete form._doc.form_id;
            const form_id = generateUUID();
            const data = {
              ...form._doc,
              form_id,
              sub_forms: [],
              created_by,
              original_form_id: _id,
              organization: organization_id,
              status: true,
            };
            const nform = await this.OrgFormsRepository.create(data);
            const d = await this.OrgFormsRepository.findOneAndUpdate(
              { form_id: sub_form_id.form_id },
              {
                $set: {
                  sub_forms: nform,
                },
              },
            );
            await this.OrgFormsRepository.findOneAndUpdate(
              { form_id: nform.form_id },
              { main_form_id: d._id },
            );
          });
        }
      }
      return isInserted;
    } catch (err) {
      throw err;
    }
  }

  /**
   * update multiple forms at a time by original forms id and origanization id
   * @param data string[]
   * @param organization_id  strinf
   * @returns Promise<Boolean>
   */
  async updateFormByFormIds(data: string[], organization_id: string) {
    try {
      const orgForms = await this.find(
        {
          organization: organization_id,
        },
        false,
        { original_form_id: true, status: true },
        ['original_form_id'],
      );
      const form_ids = [];
      orgForms.forEach((form: any) => {
        form_ids.push({
          id: form.original_form_id.form_id,
          _id: form._id,
          status: form.status,
        });
      });
      const newForms = data.filter((form: any) => {
        const newdata = form_ids
          .map((f) => {
            if (f.id === form) {
              return f.id;
            }
            return null;
          })
          .filter((d) => d);
        return !newdata.includes(form);
      });
      const removedForms = form_ids
        .map((form: any) => {
          if (!data.includes(form.id)) {
            return form;
          }
        })
        .filter((form) => form);
      let existedForms = data.filter((form: any) => {
        const newdata = form_ids
          .map((f) => {
            if (f.id === form) {
              return f.id;
            }
            return null;
          })
          .filter((d) => d);
        return newdata.includes(form);
      });
      existedForms = form_ids.filter((form) => existedForms.includes(form.id));
      if (removedForms.length) {
        removedForms.forEach(async (form) => {
          await this.findByIdAndUpdate(form._id, {
            status: false,
          });
        });
      }
      if (existedForms.length) {
        existedForms.forEach(async (form: any) => {
          if (!form.status)
            await this.findByIdAndUpdate(form._id, {
              status: true,
            });
        });
      }
      if (newForms.length) {
        await this.insertFormByFormIds(newForms, organization_id);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove form from the organizatoion
   * @param organiztion_id string
   * @param form_id string
   * @param user Express.user
   * @returns Promise<Boolean>
   */
  async removeForm(
    organization_id: string,
    form_id: string | FormsRepository,
    user: Express.User,
  ): Promise<Boolean> {
    try {
      let orginal_form: any = form_id;
      const condition: QueryOptions<OrgFormsRepository> = {
        organization: organization_id,
      };
      if (typeof form_id === 'string') {
        orginal_form = await this.FormsRepository.findOne({
          form_id,
        });
        if (orginal_form) {
          condition.original_form_id = orginal_form._id;
        } else {
          if (user['isOrganization']) {
            const form = await this.OrgFormsRepository.findOne({
              form_id,
            });
            if (form) {
              condition.form_id = form_id;
            } else {
              throw {
                status: HttpStatus.BAD_REQUEST,
                message: {
                  statusCode: HttpStatus.BAD_REQUEST,
                  message: ['Please select the existed form only'],
                  error: 'Bad Request',
                },
              };
            }
          } else {
            throw {
              status: HttpStatus.BAD_REQUEST,
              message: {
                statusCode: HttpStatus.BAD_REQUEST,
                message: ['Please select the existed form only'],
                error: 'Bad Request',
              },
            };
          }
        }
      } else {
        condition.original_form_id = orginal_form._id;
      }
      const organization_form = await this.OrgFormsRepository.findOne(
        condition,
      );
      if (!organization_form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Requested form was not Listed in Organization',
        };
      const trashed_by = getCreatedByFromUser(user);
      const updatdForm = await this.OrgFormsRepository.findByIdAndUpdate(
        organization_form._id,
        {
          deleted_at: Date.now(),
          $addToSet: {
            trashed_by,
          },
        },
        {
          new: true,
        },
      ).catch((error) => {
        throw error;
      });
      if (updatdForm) {
        if (updatdForm.has_sub_forms) {
          await this.OrgFormsRepository.updateMany(
            { _id: { $in: updatdForm.sub_forms } },
            {
              deleted_at: Date.now(),
              $addToSet: {
                trashed_by,
              },
            },
            {
              new: true,
            },
          ).catch((error) => {
            throw error;
          });
        }
        return true;
      }
      return false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add Form to Organization
   * @param organization_id string
   * @param form_id srtring
   * @param user Express.User
   * @returns Promise<Boolean>
   */
  async addForm(
    organization_id: string,
    form_id: string | FormsRepository,
    user: Express.User,
  ): Promise<OrgFormsRepository> {
    try {
      let orginal_form: any = form_id;
      const condition: QueryOptions<OrgFormsRepository> = {
        organization: organization_id,
      };
      if (typeof form_id === 'string') {
        orginal_form = await this.FormsRepository.findOne({
          form_id,
        });
        if (orginal_form) {
          condition.original_form_id = orginal_form._id;
        } else {
          if (user['isOrganization']) {
            const form = await this.OrgFormsRepository.findOne({
              form_id,
            });
            if (form) {
              condition.form_id = form_id;
            } else {
              throw {
                status: HttpStatus.BAD_REQUEST,
                message: {
                  statusCode: HttpStatus.BAD_REQUEST,
                  message: ['Please select the existed form only'],
                  error: 'Bad Request',
                },
              };
            }
          } else {
            throw {
              status: HttpStatus.BAD_REQUEST,
              message: {
                statusCode: HttpStatus.BAD_REQUEST,
                message: ['Please select the existed form only'],
                error: 'Bad Request',
              },
            };
          }
        }
      } else {
        condition.original_form_id = orginal_form._id;
      }
      const organization_form = await this.OrgFormsRepository.findOne(
        condition,
      );
      if (organization_form) {
        if (organization_form.deleted_at === null) {
          throw {
            status: HttpStatus.BAD_REQUEST,
            message: 'Form was already existed in Organization',
          };
        }
        const updatdForm = await this.OrgFormsRepository.findByIdAndUpdate(
          organization_form._id,
          {
            deleted_at: null,
            $addToSet: {
              restored_by: getCreatedByFromUser(user),
            },
          },
        ).catch((error) => {
          throw error;
        });
        if (updatdForm.has_sub_forms) {
          await this.OrgFormsRepository.updateMany(
            { _id: { $in: updatdForm.sub_forms } },
            {
              deleted_at: null,
              $addToSet: {
                restored_by: getCreatedByFromUser(user),
              },
            },
          );
        }
        return updatdForm;
      }
      const original_form_id = orginal_form?._id;
      delete orginal_form?._id;
      delete orginal_form?._doc?._id;
      const created_by = getCreatedByFromUser(user);
      const nform_id = generateUUID();
      const savedData = {
        ...orginal_form._doc,
        form_id: nform_id,
        sub_forms: [],
        original_form_id,
        organization: organization_id,
        created_by,
        status: true,
      };
      const data = await this.OrgFormsRepository.create(savedData).catch(
        (error) => {
          throw error;
        },
      );
      if (data) {
        if (orginal_form.has_sub_forms) {
          const sub_forms = await this.FormsRepositoryService.list(
            {
              _id: { $in: orginal_form.sub_forms },
            },
            false,
            {
              ...this.FormsRepositoryService.select,
              _id: true,
              sub_forms: true,
            },
          );
          sub_forms.map(async (form: any) => {
            const _id = form._id;
            delete form._doc._id;
            delete form._doc.form_id;
            const form_id = generateUUID();
            const ndata = {
              ...form._doc,
              form_id,
              sub_forms: [],
              created_by,
              original_form_id: _id,
              organization: organization_id,
              status: true,
            };
            const nform = await this.OrgFormsRepository.create(ndata);
            await this.OrgFormsRepository.findOneAndUpdate(
              { form_id: data.form_id },
              {
                $addToSet: {
                  sub_forms: nform,
                },
              },
            );
            await this.OrgFormsRepository.findOneAndUpdate(
              { form_id: nform.form_id },
              { main_form_id: data._id },
            );
          });
        }
        return data;
      }
    } catch (error) {
      throw error;
    }
  }

  /** */
  async autoCreate(
    body: AutoCreateFormsRepoDto,
    organization: string,
  ): Promise<OrgFormsRepository> {
    const data: AUTO_CREATE_FORM =
      await this.globalService.generateAutoCreateFormData(body, organization);
    const form = await this.OrgFormsRepository.create({
      ...data,
      organization,
    }).catch((error) => {
      throw error;
    });
    if (form) {
      if (data.is_sub_form) {
        const data = await this.OrgFormsRepository.findOneAndUpdate(
          { form_id: body.main_form_id },
          {
            $addToSet: {
              sub_forms: form,
            },
          },
        );
        await this.OrgFormsRepository.findOneAndUpdate(
          { form_id: form.form_id },
          { main_form_id: data._id },
        );
      }
      return form;
    }
    throw {
      status: HttpStatus.CONFLICT,
      message: 'Unable to create the Form',
    };
  }

  /**
   * Patch the form details except fields
   * @param organization string
   * @param form_id string
   * @param body FormDetails
   * @returns Promise<OrgFormsRepository>
   */
  async patchForm(
    organization: string,
    form_id: string,
    body: FormDetails,
  ): Promise<OrgFormsRepository> {
    try {
      const form = await this.findOne({ form_id, organization });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };
      const data = await this.globalService.generateFormDetail(body, form);
      return await this.OrgFormsRepository.findOneAndUpdate(
        { form_id, organization },
        data,
        {
          new: true,
        },
      ).catch((error) => {
        throw error;
      });
    } catch (error) {
      throw error;
    }
  }

  async addField(
    organization: string,
    form_id: string,
    body: AutoFieldDto,
  ): Promise<FIELD> {
    try {
      const form = await this.findOne({ form_id, organization }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };
      if (form.has_sub_forms)
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'Unable to add field due requested for has sub forms. Please try to add fields in sub forms',
        };
      const field = body.field;
      const group_title = body.group_title || form.name;
      const group_key = body.group_key || formatStringToKey(group_title);
      const group_description = body.group_description || '';
      const form_fields = form.fields;
      const form_field: FIELD = {
        ...field,
        name: `${field.name}_${Date.now()}`,
        field_id: generateUUID(),
        field_index:
          field.field_index ||
          (form_fields && form_fields[group_key]?.fields?.length) ||
          0,
      };
      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        form_field,
      );
      if (Object.keys(form_fields).length) {
        if (form_fields[group_key]) {
          const key = `fields.${group_key}.fields`;
          const data = await this.OrgFormsRepository.findOneAndUpdate(
            { form_id: form.form_id },
            {
              $push: {
                [key]: form_field,
              },
              validationschema,
            },
            {
              new: true,
            },
          )
            .select(this.select)
            .catch((error) => {
              throw error;
            });
          if (data) {
            return form_field;
          }
          throw {
            status: HttpStatus.CONFLICT,
            message: 'Unable to add the field',
          };
        } else {
          const data = await this.createNewGroupAndUpdate(
            group_title,
            group_key,
            form_field,
            form,
            group_description,
          );
          if (data) {
            return form_field;
          }
          throw {
            status: HttpStatus.CONFLICT,
            message: 'Unable to add the field',
          };
        }
      } else {
        const data = await this.createNewGroupAndUpdate(
          group_title,
          group_key,
          form_field,
          form,
          group_description,
        );
        if (data) {
          return form_field;
        }
        throw {
          status: HttpStatus.CONFLICT,
          message: 'Unable to add the field',
        };
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update Field
   * @param organization string
   * @param form_id string
   * @param body UpdaeField
   * @returns Promise<FIELD>
   */
  async updateField(
    organization: string,
    form_id: string,
    body: UpdateField,
  ): Promise<FIELD> {
    try {
      delete body?.name;
      const group_key = body.group_key;
      const field_id = body.field_id;
      const key = `fields.${group_key}.fields`;
      const form = await this.findFieldInFormWithIds(
        organization,
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      let updated_field: FIELD;
      const fields = form.fields[group_key].fields.map((field: FIELD) => {
        if (field.field_id === field_id) {
          updated_field = {
            ...field,
            ...body,
          };
          return updated_field;
        } else {
          return field;
        }
      });
      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        updated_field,
      );
      const data = await this.OrgFormsRepository.findOneAndUpdate(
        { form_id },
        {
          [key]: fields,
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (body.auto_fill) {
        const targetField = form.fields[group_key].fields.find(
          (f) => f.field_id === field_id,
        );

        const autoFillDep = {
          enabled: body.auto_fill.enabled,
          source_form_id: form_id,
          source_form_name: form.name,
          source_section_name: form.fields[group_key].group_title,
          source_section_key: group_key,
          source_field_name: targetField.name,
          source_field_id: field_id,
        };

        const result = await this.OrgFormsRepository.aggregate([
          { $match: { form_id: body.auto_fill.source_form_id } },
          {
            $project: {
              [`field`]: {
                $filter: {
                  input: `$fields.${body.auto_fill.source_section_key}.fields`,
                  as: 'f',
                  cond: {
                    $eq: ['$$f.field_id', body.auto_fill.source_field_id],
                  },
                },
              },
            },
          },
        ]);
        const matchingField = result[0]?.field[0];
        matchingField['auto_fill_dependencies'] = [];
        if (
          matchingField['auto_fill_dependencies'] &&
          matchingField['auto_fill_dependencies'].length > 0
        ) {
          const existedField = matchingField['auto_fill_dependencies'].find(
            (f) => f.field_id === field_id,
          );
          if (!existedField) {
            matchingField['auto_fill_dependencies'].push(autoFillDep);
          }
        } else {
          matchingField['auto_fill_dependencies'].push(autoFillDep);
        }

        const update = await this.OrgFormsRepository.findOneAndUpdate(
          {
            form_id: body.auto_fill.source_form_id,
            [`fields.${body.auto_fill.source_section_key}.fields.field_id`]:
              body.auto_fill.source_field_id,
          },
          {
            $set: {
              [`fields.${body.auto_fill.source_section_key}.fields.$[elem]`]: {
                ...matchingField,
              },
            },
          },
          {
            arrayFilters: [{ 'elem.field_id': body.auto_fill.source_field_id }],
            new: true,
          },
        );
      }
      if (data) return updated_field;
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to update the field',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create new field in form by group
   * @param group_title string
   * @param field FIELD
   * @param group_key string
   * @param form OrgFormsRepository
   * @returns Promis<OrgFormsRepository>
   */
  async createNewGroupAndUpdate(
    group_title: string,
    group_key: string,
    field: FIELD,
    form: OrgFormsRepository,
    group_description: string,
  ): Promise<OrgFormsRepository> {
    try {
      const group: GROUP = {
        group_title: group_title,
        group_key,
        group_id: generateUUID(),
        fields: [field],
        group_index: (form?.fields && Object.keys(form?.fields)?.length) || 0,
        group_description,
      };
      let group_validations = {
        [field.name]: {
          type: field?.input_type || 'text',
          ...field.validation_schema,
        },
      };
      if (form.validationschema && form.validationschema[group_key]) {
        group_validations = {
          ...form.validationschema[group_key],
          ...group_validations,
        };
      }
      const validationschema = {
        ...form.validationschema,
        [group_key]: group_validations,
      };
      return await this.OrgFormsRepository.findOneAndUpdate(
        { form_id: form.form_id },
        {
          fields: {
            ...form.fields,
            [group_key]: group,
          },
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check field in form or not
   * @param organization string
   * @param form_id string
   * @param field_id string
   * @param key string
   * @returns Promise<OrgFormsRepository>
   */
  async findFieldInFormWithIds(
    organization: string,
    form_id: string,
    field_id: string,
    group_key: string,
  ): Promise<OrgFormsRepository> {
    const key = `fields.${group_key}.fields`;
    const find = {
      organization,
      form_id,
      [key]: {
        $elemMatch: { field_id },
      },
    };
    return await this.findOne(find);
  }

  async getUserForms(
    user: Express.User,
    isClientRequired = false,
    client_id?: string,
    is_quiz_form?: boolean,
    from?: string,
  ): Promise<any[]> {
    const findBy: QueryOptions<OrgFormsRepository> = {
      organization: user['organization'],
      app_id: user['app_id'],
      status: true,
    };

    if (is_quiz_form) {
      findBy.is_quiz_form = true;
    } else {
      findBy.$or = [
        { is_quiz_form: { $ne: true } },
        { is_quiz_form: { $exists: false } },
      ];
    }

    if (from == 'email') {
      findBy.has_sub_forms = false;
    } else {
      findBy.is_sub_form = false;
    }

    let client_details: any;

    if (isClientRequired && client_id) {
      client_details = await this.clientsRepository.findOne({
        client_id,
      });
    }

    let forms: OrgFormsRepository[] = await this.find(
      findBy,
      false,
      {
        ...this.select,
        _id: true,
      },
      ['sub_forms'],
    );

    const toggleDetails: AppOrgFormsToggleRepository =
      await this.appOrgFormsToggleRepository.findOne({
        app_id: user['app_id'],
        organization_id: user['organization'],
      });

    if (toggleDetails && toggleDetails.status) {
      forms = forms.sort((a, b) => a.order_position - b.order_position);
    }

    const isFormRequired = (form: OrgFormsRepository) => {
      return (
        form?.fields &&
        Object.values(form?.fields).some((section) =>
          section?.fields?.some((field) => field?.validation_schema?.required),
        )
      );
    };

    const getCompleted = (
      form: OrgFormsRepository,
      formValues: FormValuesRepository,
    ): boolean => {
      if (formValues) {
        if (formValues.completed) {
          return formValues.completed;
        }
        const values: any = formValues.values;
        const keys = Object.keys(formValues?.values);
        if (keys.length) {
          keys.map((key) => {
            const requiredFieldsKey = form.fields[key].fields.filter(
              (field) => field.validation_schema.required,
            );
            requiredFieldsKey.forEach((field: FIELD) => {
              return field.is_iterative_or_not
                ? values[key][field.name].length > 0
                  ? true
                  : false
                : values[key][field.name]
                ? true
                : false;
            });
          });
        }
      }
      return false;
    };

    return await Promise.all(
      forms.map(async (form: OrgFormsRepository) => {
        let completed: boolean = true;

        let required: boolean = true;

        if (form.sub_forms && form.sub_forms.length) {
          await Promise.all(
            form.sub_forms.map(async (sub_form: OrgFormsRepository) => {
              const condition: QueryOptions<OrgFormsRepository> = {
                organization_id: user['organization'],
                form: sub_form['_id'],
              };

              if (isClientRequired) {
                condition.client = client_details?._id;
              } else {
                condition.user_id = user['user_id'];
              }

              const formValues = await this.formValueRepository.findOne(
                condition,
              );

              if (!getCompleted(sub_form, formValues)) {
                completed = false;
              }

              if (!isFormRequired(sub_form)) {
                required = false;
              }
            }),
          );
        } else {
          const condition: QueryOptions<OrgFormsRepository> = {
            organization_id: user['organization'],
            form: form['_id'],
          };

          if (isClientRequired) {
            condition.client = client_details?._id;
          } else {
            condition.user_id = user['user_id'];
          }

          const formValues = await this.formValueRepository.findOne(condition);

          required = isFormRequired(form);

          completed = getCompleted(form, formValues);
        }

        const returnData = {
          name: form.name,
          status: form.status,
          app_id: form.app_id,
          form_id: form.form_id,
          icon: form?.icon,
          completed,
          required,
          is_sub_form: form.is_sub_form,
          is_quiz_form: form?.is_quiz_form,
          tabletLayout: form.tabletLayout,
          has_sub_forms: form.has_sub_forms,
          sectionViewType: form.sectionViewType,
        };

        return returnData;
      }),
    );
  }

  async getSubForms(
    user: Express.User,
    form_id: string,
    isClientRequired = false,
    client_id?: string,
  ): Promise<any[]> {
    try {
      let client_details: any;

      if (isClientRequired && client_id) {
        client_details = await this.clientsRepository.findOne({
          client_id,
        });
      } else if (isClientRequired && !client_id) {
        throw new HttpException(
          'Client Id is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const forms = await this.findOne(
        {
          form_id,
          has_sub_forms: true,
          app_id: user['app_id'],
          organization: user['organization'],
          status: true,
        },
        false,
        {
          sub_forms: true,
          app_id: true,
          form_id: true,
          _id: false,
          fields: true,
        },
        {
          path: 'sub_forms',
          select: 'form_id name status has_sub_forms fields icon',
          match: {
            status: true,
          },
        },
      );

      const isFormRequired = (form: OrgFormsRepository) => {
        return (
          form?.fields &&
          Object.values(form?.fields).some((section) =>
            section?.fields?.some(
              (field) => field?.validation_schema?.required,
            ),
          )
        );
      };

      const getCompleted = (
        form: OrgFormsRepository,
        formValues: FormValuesRepository,
      ): boolean => {
        if (formValues) {
          if (formValues.completed) {
            return formValues.completed;
          }
          const values: any = formValues.values;
          const keys = Object.keys(formValues?.values);
          if (keys.length) {
            keys.map((key) => {
              const requiredFieldsKey = form.fields[key].fields.filter(
                (field) => field.validation_schema.required,
              );
              requiredFieldsKey.forEach((field: FIELD) => {
                return field.is_iterative_or_not
                  ? values[key][field.name].length > 0
                    ? true
                    : false
                  : values[key][field.name]
                  ? true
                  : false;
              });
            });
          }
        }
        return false;
      };

      return await Promise.all(
        forms.sub_forms.map(async (form: OrgFormsRepository) => {
          const condition: QueryOptions<OrgFormsRepository> = {
            organization_id: user['organization'],
            form: form['_id'],
          };

          if (isClientRequired) {
            condition.client = client_details?._id;
          } else {
            condition.user_id = user['user_id'];
          }

          const formValues = await this.formValueRepository.findOne(condition);

          const returnData = {
            name: form.name,
            icon: form?.icon,
            status: form.status,
            app_id: form.app_id,
            form_id: form.form_id,
            required: isFormRequired(form),
            completed: getCompleted(form, formValues),
            is_sub_form: form.is_sub_form,
            tabletLayout: form.tabletLayout,
            has_sub_forms: form.has_sub_forms,
            sectionViewType: form.sectionViewType,
          };

          return returnData;
        }),
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  async getUserFormsV2(
    user: Express.User,
    isClientRequired = false,
    primary_form_id?: string,
    is_quiz_form?: boolean,
  ): Promise<any[]> {
    try {
      const findBy: QueryOptions<OrgFormsRepository> = {
        organization: user['organization'],
        app_id: user['app_id'],
        status: true,
        is_sub_form: false,
      };

      if (is_quiz_form) {
        findBy.is_quiz_form = true;
      } else {
        findBy.$or = [
          { is_quiz_form: { $ne: true } },
          { is_quiz_form: { $exists: false } },
        ];
      }

      const forms = await this.find(findBy, false, {
        ...this.select,
        _id: true,
      });

      let primaryForm: PrimaryFormsRepository;

      if (primary_form_id) {
        primaryForm = await this.primaryFormsRepository.findOne({
          primary_form_id,
        });
      }

      return await Promise.all(
        forms.map(async (form: OrgFormsRepository) => {
          const condition: QueryOptions<FormValuesRepository> = {
            organization_id: user['organization'],
            form: form['_id'],
          };

          if (isClientRequired) {
            condition.primary_form = primaryForm;
          } else {
            condition.user_id = user['user_id'];
          }

          const formValues: FormValuesRepository =
            await this.formValueRepository.findOne(condition);

          const getCompleted = () => {
            if (formValues) {
              if (formValues.completed) {
                return formValues.completed;
              }

              const values: any = formValues?.values_encryption_status
                ? this.encryptionService.decryption(formValues.values)
                : formValues?.values;

              const keys = Object.keys(values);

              if (keys.length) {
                keys.map((key) => {
                  const requiredFieldsKey = form.fields[key].fields.filter(
                    (field) => field.validation_schema.required,
                  );
                  requiredFieldsKey.forEach((field: FIELD) => {
                    return field.is_iterative_or_not
                      ? values[key][field.name].length > 0
                        ? true
                        : false
                      : values[key][field.name]
                      ? true
                      : false;
                  });
                });
              }
            }
            return false;
          };

          const returnData = {
            name: form.name,
            status: form.status,
            app_id: form.app_id,
            form_id: form.form_id,
            completed: getCompleted(),
            is_sub_form: form.is_sub_form,
            tabletLayout: form.tabletLayout,
            has_sub_forms: form.has_sub_forms,
            sectionViewType: form.sectionViewType,
            order_position: form.order_position,
          };

          return returnData;
        }),
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  async getUserPrimaryForm(
    user: Express.User,
    app_id: string,
    primary_form_id?: string,
  ): Promise<any> {
    try {
      const organization = user['isOrganization']
        ? user['organization_id']
        : user['organization']['organization_id'];

      if (!organization) {
        throw new UnauthorizedException('Invalid organization details');
      }

      if (!app_id) {
        throw new BadRequestException('App id is required');
      }

      const findBy: QueryOptions<OrgFormsRepository> = {
        organization,
        app_id,
        status: true,
        is_sub_form: false,
        deleted_at: { $eq: null },
      };

      let user_forms: any = await this.OrgFormsRepository.find(findBy)
        .sort({ position_order: 1 })
        .limit(1);

      if (user_forms && user_forms.length) {
        const user_primary_form: any = user_forms[0];
        if (primary_form_id) {
          const condition: QueryOptions<OrgFormsRepository> = {
            organization_id: user['organization'],
            form: user_primary_form['_id'],
          };

          if (primary_form_id) {
            condition.primary_form_id = primary_form_id;
          }

          const formValues: FormValuesRepository =
            await this.formValueRepository.findOne(condition);

          user_primary_form.values = formValues || null;
        }

        const sections = user_primary_form?.fields;

        const sectionsKey = sections && Object.keys(sections);

        const groups =
          sectionsKey &&
          sectionsKey.map((key: string): GROUP => {
            const fields = user_primary_form.fields[key].fields.sort(
              (f1: FIELD, f2: FIELD) =>
                f1.field_index > f2.field_index ? 1 : -1,
            );

            return {
              ...user_primary_form.fields[key],
              fields: fields,
              group_key: key,
              group_description:
                user_primary_form?.fields[key]?.group_description || '',
            };
          });

        return {
          groups,
          name: user_primary_form.name,
          app_id: user_primary_form.app_id,
          status: user_primary_form.status,
          form_id: user_primary_form.form_id,
          has_sub_forms: user_primary_form.has_sub_forms,
          sectionViewType: user_primary_form.sectionViewType,
          tabletLayout: user_primary_form.tabletLayout,
        };
      } else {
        throw new Error('No primary form found');
      }
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  async saveData1(
    form_id: string,
    body: any,
    user: Express.User,
    isClientRequired: boolean,
  ): Promise<any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }

      let total_points: number;
      let scored_points: number;

      const form = await this.findOne(
        {
          form_id,
          organization: user['organization'],
          app_id: user['app_id'],
          status: true,
        },
        false,
        { ...this.select, _id: true },
        'form_response_as_email',
      );

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      if (form.is_quiz_form) {
        const scoreResults = this.calculateTotalScoreAndPoints(
          form.fields,
          body,
        );

        total_points = scoreResults?.totalPoints;
        scored_points = scoreResults?.scoredPoints;

        let score_text: string = '';

        // if (total_points && scored_points != 0) {
        //   score_text = `We’re pleased to share that you have secured <b>${scored_points}</b> out of <b>${total_points}</b> points.`;
        // }

        // const mail = {
        //   to: user['email'],
        //   subject: `${form?.name}`,
        //   from: '',
        //   text: 'Hai',
        //   html: `
        //     <html>
        //       <head>
        //       </head>
        //       <p>Dear ${user['name'].toUpperCase()},
        //       <br/>
        //       <p>Thank you for completing the ${form?.name}! ${score_text} </p>
        //       <br/>
        //       <p>Your results give us insight into your understanding of our organization's values and processes. Whether you've scored high or see room for improvement, we're here to support your growth and success. </p>
        //       <br/>
        //       <p>Once again, welcome to the team! We’re excited to work with you and look forward to your contributions.</p>
        //       <br/>
        //       <p>Best Regards,  </p>
        //       <p>The Kleza FAB Team </p>
        //       </body>
        //     </html>
        //   `,
        // };

        // await this.sendGridService.sendWithOrganizationId(
        //   user['organization'],
        //   mail,
        // );
      }

      let client: any;

      let formValueFindConditions: any = {
        form: form['_id'],
        organization_id: user['organization'],
      };

      //form completed status
      const completed = !isObjectEmpty(body);

      let createOrUpdateFormValue: any = {
        values: body,
        completed,
        total_points,
        scored_points,
      };

      let createOrUpdateFormValueElse: any = {
        form,
        user_id: user['user_id'],
        organization_id: user['organization'],
        values: body,
        completed,
        total_points,
        scored_points,
        form_value_id: generateUUID(),
      };

      if (isClientRequired) {
        client = await this.clientsRepository.findOne({
          client_id: body.client_id,
        });

        if (!client)
          throw { status: HttpStatus.NOT_FOUND, message: 'Client Not Found' };

        formValueFindConditions.client = client._id;

        createOrUpdateFormValue.client = client._id;

        createOrUpdateFormValueElse.client = client._id;
      } else {
        formValueFindConditions.user_id = user['user_id'];
      }

      const value = await this.formValueRepository.findOne(
        formValueFindConditions,
      );

      const errors = await this.globalService.validate(
        form.validationschema,
        body,
        form,
        value?.form_value_id,
        user['organization'],
      );

      if (Object.keys(errors).length) {
        return errors;
      } else {
        delete body?.client_id;

        if (form?.form_response_as_email) {
          const app_id = user['app_id'];
          const organization_id = user['organization'];
          // // Check Organization details exist or not
          // const organizationDetails = await this.orgService.findOne(
          //   { organization_id },
          //   false,
          //   { ...this.orgService.select, id: true },
          // );

          // if (!organizationDetails) {
          //   throw new Error('Organization details not found');
          // }

          // //Check app details exists or not
          // const appDetails = await this.appsService.findOne({ app_id }, false, {
          //   ...this.appsService.select,
          //   id: true,
          // });

          // if (!appDetails) {
          //   throw new Error('App details not found');
          // }

          // //Checking Organization - App have custom details
          // const customOrgAppConfig =
          //   await this.orgAppConfigurationRepository.findOne({
          //     where: {
          //       app: { id: appDetails?.id },
          //       organization: { id: organizationDetails?.id },
          //     },
          //   });

          const formDetailsForEmail = {
            form,
            values: body,
          };

          //Check organization details exists or not
          const organizationDetails = await this.orgService.findOne(
            {
              organization_id: user['organization'],
            },
            false,
            { ...this.orgService.select, id: true },
          );

          if (!organizationDetails) {
            throw new Error('Organization details not found');
          }

          const appDetails = await this.appsService.findOne(
            {
              app_id: user['app_id'],
            },
            false,
            {
              ...this.appsService.select,
              name: true,
              id: true,
            },
          );

          if (!appDetails) {
            throw new Error('App details not found');
          }

          //Checking Organization - App have custom details
          const customOrgAppConfig =
            await this.orgAppConfigurationRepository.findOne({
              where: {
                app: { id: appDetails?.id },
                organization: { id: organizationDetails?.id },
              },
            });

          const base64Logo = await this.getBase64(
            'https://app.klezafab.com/assets/fab-logo.png',
          );
          const logoSrc = `data:image/png;base64,${base64Logo}`;

          const logo =
            customOrgAppConfig?.logo && customOrgAppConfig.logo !== ''
              ? customOrgAppConfig.logo
              : logoSrc;

          const options = {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: `<div class="header" 
                  style="border-bottom: 2px solid #000;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative; width:100%;margin:20px;height:38px;margin-top:0px">
                <div class="logo-container" style="position: absolute;
                  left: 0;">
                  <img src=${logo} width="140px" height="28px" alt="Kleza FAB">
                </div>
                <div class="title-container" style="position: absolute;">
                  <h2 style="font-size:18px">${form?.name.toUpperCase()}</h2>
                </div>
              </div>`,
            footerTemplate: `
              <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                <div style="flex: 1; text-align: center;">
                 <b>Employee Onboarding</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
              </div>
            `,
            margin: {
              top: '20mm',
              right: '10mm',
              bottom: '15mm',
              left: '10mm',
            },
          };

          // Generate the HTML content
          const file = {
            content: await this.generateHtml(formDetailsForEmail),
          };

          // Generate the PDF buffer
          const pdfBuffer = await pdf.generatePdf(file, options);

          // Convert PDF buffer to base64
          const pdfBase64 = pdfBuffer.toString('base64');

          // Sanitize file name
          const fileName = `${sanitizeFileName(form.name?.trim())}`;

          // Send email with the PDF attachment
          const email: any = {
            to: form?.form_response_as_email.sent_to_user
              ? user['email']
              : form?.form_response_as_email.sent_to_user_or_other,
            from: '',
            cc: form?.form_response_as_email?.cc_emails || [],
            subject: form.name.toUpperCase(),
            text: 'Please find the attached PDF document.',
            attachments: [
              {
                content: pdfBase64,
                filename: `${fileName}.pdf`,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ],
          };

          await this.sendGridService.sendWithOrganizationId(
            user['organization'],
            email,
          );
        }

        let form_value: any;

        if (value) {
          form_value = await this.formValueRepository.findOneAndUpdate(
            {
              _id: value._id,
            },
            createOrUpdateFormValue,
          );
        } else {
          form_value = await this.formValueRepository.create(
            createOrUpdateFormValueElse,
          );

          if (isClientRequired) {
            await this.clientsRepository.findOneAndUpdate(
              { client_id: client.client_id },
              {
                $addToSet: { form_values: form_value._id },
              },
            );
          }
        }

        if (form.is_quiz_form) {
          let onboarding_employee: any =
            await this.onboardingEmployeeService.findOne({
              onboarding_employee_id: user['user_id'],
            });

          const organizations = await this.orgService.findOne(
            {
              organization_id: user['organization'],
            },
            false,
            { id: true },
          );

          const onboarding_employees =
            await this.onboardingEmployeeService.employeesWithFormStatus(
              [onboarding_employee],
              organizations,
            );

          onboarding_employee = onboarding_employees?.[0];

          if (
            onboarding_employee &&
            onboarding_employee?.orientation_status &&
            onboarding_employee?.orientation_status === 'Orientation Completed'
          ) {
            try {
              const mail = {
                to: onboarding_employee.email,
                subject: 'Orientation completed successfully',
                from: '',
                text: 'Hai',
                html: `
                        <html>
                              <head></head>
                              <body>
                                  <p>Dear ${onboarding_employee.name.toUpperCase()},</p>
                                  <br/>
                                  <p>Congratulations! You have completed the orientation process. </p>
                                  <br/>
                                  ${
                                    onboarding_employee?.orientationResultString
                                  }
                                  <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a> </p>
                                  <br/>
                                  <p>Welcome to the team!</p>
                                  <br/>
                                  <p>Best regards,</p>
                                  <p>The Kleza FAB Team </p>
                                  </body>
                        </html>`,
              };

              const organization_id: any = user['organization'];

              await this.sendGridService.sendWithOrganizationId(
                organization_id,
                mail,
              );
            } catch (error) {
              throw new HttpException(
                error?.message ||
                  'Something went wrong. Please try again later.',
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }
          }
        }

        return form_value ? true : false;
      }
    } catch (error) {
      throw error;
    }
  }

  async saveDataWithOutValidation1(
    form_id: string,
    body: any,
    user: Express.User,
    isClientRequired: boolean,
    e_sign_request_logs?: any,
  ): Promise<any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }

      let total_points: number;
      let scored_points: number;

      const form = await this.findOne(
        {
          form_id,
          organization: e_sign_request_logs
            ? e_sign_request_logs?.organization_id
            : user['organization'],
          app_id: e_sign_request_logs
            ? e_sign_request_logs?.app_id
            : user['app_id'],
          status: true,
        },
        false,
        { ...this.select, _id: true },
      );

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      if (form.is_quiz_form) {
        const scoreResults = this.calculateTotalScoreAndPoints(
          form.fields,
          body,
        );

        total_points = scoreResults?.totalPoints;
        scored_points = scoreResults?.scoredPoints;

        // const mail = {
        //   to: user['email'],
        //   subject: 'Orientation Form Score Result',
        //   from: '',
        //   text: 'Hai',
        //   html: `<html>
        //           <head>
        //           </head>
        //           <p>Dear ${user['name']},
        //           <br/>
        //           <p>Thank you for completing the orientation quiz! We’re pleased to share that you have secured <b>${scored_points}</b> out of <b>${total_points}</b> points. </p>
        //           <br/>
        //           <p>Your results give us insight into your understanding of our organization's values and processes. Whether you've scored high or see room for improvement, we're here to support your growth and success. </p>
        //           <br/>
        //           <p>Once again, welcome to the team! We’re excited to work with you and look forward to your contributions.</p>
        //           <br/>
        //           <p>Best Regards,  </p>
        //           <p>The Kleza FAB Team </p>
        //           </body>
        //         </html>`,
        // };

        // await this.sendGridService.sendWithOrganizationId(
        //   user['organization'],
        //   mail,
        // );
      }

      let client: any;

      const completed = !isObjectEmpty(body);

      let formValueFindConditions: any = {
        form: form['_id'],
        organization_id: e_sign_request_logs
          ? e_sign_request_logs?.organization_id
          : user['organization'],
      };

      if (isClientRequired) {
        client = await this.clientsRepository.findOne({
          client_id: body.client_id,
        });

        if (!client)
          throw { status: HttpStatus.NOT_FOUND, message: 'Client Not Found' };

        formValueFindConditions.client = client._id;
      } else {
        formValueFindConditions.user_id = e_sign_request_logs
          ? e_sign_request_logs?.user_id
          : user['user_id'];
      }

      const value = await this.formValueRepository.findOne(
        formValueFindConditions,
      );

      const resData =
        await this.globalService.validateAndEliminationOfUnknownValues(
          form.validationschema,
          body,
          form,
          value?.form_value_id,
          e_sign_request_logs
            ? e_sign_request_logs?.organization_id
            : user['organization'],
        );

      if (Object.keys(resData?.errors).length) {
        return resData?.errors;
      } else {
        if (form_id === process.env.HAND_BOOK_FORM_ID) {
          const formDetailsForEmail = {
            form,
            values: body,
          };

          //Check organization details exists or not
          const organizationDetails = await this.orgService.findOne(
            {
              organization_id: e_sign_request_logs
                ? e_sign_request_logs?.organization_id
                : user['organization'],
            },
            false,
            { ...this.orgService.select, id: true },
          );

          if (!organizationDetails) {
            throw new Error('Organization details not found');
          }

          const appDetails = await this.appsService.findOne(
            {
              app_id: e_sign_request_logs
                ? e_sign_request_logs?.app_id
                : user['app_id'],
            },
            false,
            {
              ...this.appsService.select,
              name: true,
              id: true,
            },
          );

          if (!appDetails) {
            throw new Error('App details not found');
          }

          //Checking Organization - App have custom details
          const customOrgAppConfig =
            await this.orgAppConfigurationRepository.findOne({
              where: {
                app: { id: appDetails?.id },
                organization: { id: organizationDetails?.id },
              },
            });

          const base64Logo = await this.getBase64(
            'https://app.klezafab.com/assets/fab-logo.png',
          );
          const logoSrc = `data:image/png;base64,${base64Logo}`;

          const logo =
            customOrgAppConfig?.logo && customOrgAppConfig.logo !== ''
              ? customOrgAppConfig.logo
              : logoSrc;

          const options = {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            headerTemplate: `<div class="header" 
                  style="border-bottom: 2px solid #000;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative; width:100%;margin:20px;height:38px;margin-top:0px">
                <div class="logo-container" style="position: absolute;
                  left: 0;">
                  <img src=${logo} width="140px" height="28px" alt="Kleza FAB">
                </div>
                <div class="title-container" style="position: absolute;">
                  <h2 style="font-size:18px">${form?.name.toUpperCase()}</h2>
                </div>
              </div>`,
            footerTemplate: `
              <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                <div style="flex: 1; text-align: center;">
                 <b>Employee Onboarding</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
              </div>
            `,
            margin: {
              top: '20mm',
              right: '10mm',
              bottom: '15mm',
              left: '10mm',
            },
          };

          // Generate the HTML content
          const file = {
            content: await this.generateHtml(formDetailsForEmail),
          };

          // Generate the PDF buffer
          const pdfBuffer = await pdf.generatePdf(file, options);

          // Convert PDF buffer to base64
          const pdfBase64 = pdfBuffer.toString('base64');

          // Sanitize file name
          const fileName = `${sanitizeFileName(form.name?.trim())}.pdf`;

          // Send email with the PDF attachment
          const email: any = {
            to: user['email'],
            from: '',
            subject: 'Assured Home Nursing Handbook',
            text: 'Please find the attached PDF document.',
            attachments: [
              {
                content: pdfBase64,
                filename: `${fileName}.pdf`,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ],
          };

          await this.sendGridService.sendWithOrganizationId(
            user['organization'],
            email,
          );
        }

        let form_value: any;

        if (value) {
          form_value = await this.formValueRepository.findOneAndUpdate(
            {
              _id: value._id,
            },
            {
              client: isClientRequired ? client._id : null,
              values: resData?.reqValues,
              completed,
              total_points,
              scored_points,
              e_sign_request_logs: e_sign_request_logs
                ? e_sign_request_logs
                : null,
            },
          );
        } else {
          form_value = await this.formValueRepository.create({
            form,
            client: isClientRequired ? client._id : null,
            user_id: e_sign_request_logs
              ? e_sign_request_logs?.user_id
              : user['user_id'],
            organization_id: e_sign_request_logs
              ? e_sign_request_logs?.organization_id
              : user['organization'],
            values: resData?.reqValues,
            completed,
            total_points,
            scored_points,
            form_value_id: generateUUID(),
            e_sign_request_logs: e_sign_request_logs
              ? e_sign_request_logs
              : null,
          });

          if (isClientRequired) {
            await this.clientsRepository.findOneAndUpdate(
              { client_id: client.client_id },
              {
                $addToSet: { form_values: form_value._id },
              },
            );
          }
        }

        if (form.is_quiz_form) {
          let onboarding_employee: any =
            await this.onboardingEmployeeService.findOne({
              onboarding_employee_id: user['user_id'],
            });

          const organizations = await this.orgService.findOne(
            {
              organization_id: user['organization'],
            },
            false,
            { id: true },
          );

          const onboarding_employees =
            await this.onboardingEmployeeService.employeesWithFormStatus(
              [onboarding_employee],
              organizations,
            );

          onboarding_employee = onboarding_employees?.[0];

          if (
            onboarding_employee &&
            onboarding_employee?.orientation_status &&
            onboarding_employee?.orientation_status === 'Orientation Completed'
          ) {
            try {
              const mail = {
                to: onboarding_employee.email,
                subject: 'Orientation completed successfully',
                from: '',
                text: 'Hai',
                html: `
                        <html>
                              <head></head>
                              <body>
                                  <p>Dear ${onboarding_employee.name.toUpperCase()},</p>
                                  <br/>
                                  <p>Congratulations! You have completed the orientation process. </p>
                                  <br/>
                                  <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a> </p>
                                  <br/>
                                  <p>Welcome to the team!</p>
                                  <br/>
                                  <p>Best regards,</p>
                                  <p>The Kleza FAB Team </p>
                                  </body>
                        </html>`,
              };

              const organization_id: any = user['organization'];

              await this.sendGridService.sendWithOrganizationId(
                organization_id,
                mail,
              );
            } catch (error) {
              throw new HttpException(
                error?.message ||
                  'Something went wrong. Please try again later.',
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }
          }
        }

        return form_value ? true : false;
      }
    } catch (error) {
      throw error;
    }
  }

  async saveData(
    form_id: string,
    body: any,
    user: Express.User,
    isClientRequired: boolean,
  ): Promise<any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }

      let total_points: number;
      let scored_points: number;

      const form = await this.findOne(
        {
          form_id,
          organization: user['organization'],
          app_id: user['app_id'],
          status: true,
        },
        false,
        { ...this.select, _id: true },
        'form_response_as_email',
      );

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      if (form.is_quiz_form) {
        const scoreResults = this.calculateTotalScoreAndPoints(
          form.fields,
          body,
        );

        total_points = scoreResults?.totalPoints;
        scored_points = scoreResults?.scoredPoints;

        let score_text: string = '';

        // if (total_points && scored_points != 0) {
        //   score_text = `We’re pleased to share that you have secured <b>${scored_points}</b> out of <b>${total_points}</b> points.`;
        // }

        // const mail = {
        //   to: user['email'],
        //   subject: `${form?.name}`,
        //   from: '',
        //   text: 'Hai',
        //   html: `
        //     <html>
        //       <head>
        //       </head>
        //       <p>Dear ${user['name'].toUpperCase()},
        //       <br/>
        //       <p>Thank you for completing the ${form?.name}! ${score_text} </p>
        //       <br/>
        //       <p>Your results give us insight into your understanding of our organization's values and processes. Whether you've scored high or see room for improvement, we're here to support your growth and success. </p>
        //       <br/>
        //       <p>Once again, welcome to the team! We’re excited to work with you and look forward to your contributions.</p>
        //       <br/>
        //       <p>Best Regards,  </p>
        //       <p>The Kleza FAB Team </p>
        //       </body>
        //     </html>
        //   `,
        // };

        // await this.sendGridService.sendWithOrganizationId(
        //   user['organization'],
        //   mail,
        // );
      }

      let client: any;

      let formValueFindConditions: any = {
        form: form['_id'],
        organization_id: user['organization'],
      };

      //form completed status
      const completed = !isObjectEmpty(body);

      let createOrUpdateFormValue: any = {
        values: body,
        completed,
        total_points,
        scored_points,
      };

      let createOrUpdateFormValueElse: any = {
        form,
        user_id: user['user_id'],
        organization_id: user['organization'],
        values: body,
        completed,
        total_points,
        scored_points,
        form_value_id: generateUUID(),
      };

      if (isClientRequired) {
        client = await this.clientsRepository.findOne({
          client_id: body.client_id,
        });

        if (!client)
          throw { status: HttpStatus.NOT_FOUND, message: 'Client Not Found' };

        formValueFindConditions.client = client._id;

        createOrUpdateFormValue.client = client._id;

        createOrUpdateFormValueElse.client = client._id;
      } else {
        formValueFindConditions.user_id = user['user_id'];
      }

      const value = await this.formValueRepository.findOne(
        formValueFindConditions,
      );

      const errors = await this.globalService.validate(
        form.validationschema,
        body,
        form,
        value?.form_value_id,
        user['organization'],
      );

      if (Object.keys(errors).length) {
        return errors;
      } else {
        delete body?.client_id;

        if (form?.form_response_as_email) {
          const app_id = user['app_id'];
          const organization_id = user['organization'];
          // Check Organization details exist or not
          const organizationDetails = await this.orgService.findOne(
            { organization_id },
            false,
            { ...this.orgService.select, id: true },
          );

          if (!organizationDetails) {
            throw new Error('Organization details not found');
          }

          //Check app details exists or not
          const appDetails = await this.appsService.findOne({ app_id }, false, {
            ...this.appsService.select,
            id: true,
          });

          if (!appDetails) {
            throw new Error('App details not found');
          }

          //Checking Organization - App have custom details
          const customOrgAppConfig =
            await this.orgAppConfigurationRepository.findOne({
              where: {
                app: { id: appDetails?.id },
                organization: { id: organizationDetails?.id },
              },
            });

          const formDetailsForEmail = {
            form,
            values: body,
          };

          const options = {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            footerTemplate: `
              <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                <div style="flex: 1; text-align: center;">
                 <b>Employee Onboarding</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
              </div>
            `,
            margin: {
              top: '10mm',
              right: '10mm',
              bottom: '15mm',
              left: '10mm',
            },
          };

          // Generate the HTML content
          const file = {
            content: await this.generateHtml(
              formDetailsForEmail,
              customOrgAppConfig,
            ),
          };

          // Generate the PDF buffer
          const pdfBuffer = await pdf.generatePdf(file, options);

          // Convert PDF buffer to base64
          const pdfBase64 = pdfBuffer.toString('base64');

          // Sanitize file name
          const fileName = `${sanitizeFileName(form.name?.trim())}`;

          // Send email with the PDF attachment
          const email: any = {
            to: form?.form_response_as_email.sent_to_user
              ? user['email']
              : form?.form_response_as_email.sent_to_user_or_other,
            from: '',
            cc: form?.form_response_as_email?.cc_emails || [],
            subject: form.name.toUpperCase(),
            text: 'Please find the attached PDF document.',
            attachments: [
              {
                content: pdfBase64,
                filename: `${fileName}.pdf`,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ],
          };

          await this.sendGridService.sendWithOrganizationId(
            user['organization'],
            email,
          );
        }

        let form_value: any;

        if (!form?.has_multiple_form_responses && value) {
          if (completed) {
            createOrUpdateFormValue.values = this.encryptionService.encryption(
              createOrUpdateFormValue?.values,
            );

            createOrUpdateFormValue.values_encryption_status = true;
          }

          form_value = await this.formValueRepository.findOneAndUpdate(
            {
              _id: value._id,
            },
            createOrUpdateFormValue,
          );
        } else {
          if (completed) {
            createOrUpdateFormValueElse.values =
              this.encryptionService.encryption(
                createOrUpdateFormValueElse?.values,
              );

            createOrUpdateFormValueElse.values_encryption_status = true;
          }

          form_value = await this.formValueRepository.create(
            createOrUpdateFormValueElse,
          );

          if (isClientRequired) {
            await this.clientsRepository.findOneAndUpdate(
              { client_id: client.client_id },
              {
                $addToSet: { form_values: form_value._id },
              },
            );
          }
        }

        if (form.is_quiz_form) {
          let onboarding_employee: any =
            await this.onboardingEmployeeService.findOne({
              onboarding_employee_id: user['user_id'],
            });

          const organizations = await this.orgService.findOne(
            {
              organization_id: user['organization'],
            },
            false,
            { id: true, name: true, email: true },
          );

          const { name: organizationName, email: organizationEmail } =
            organizations;

          delete organizations?.name;
          delete organizations?.email;

          const onboarding_employees =
            await this.onboardingEmployeeService.employeesWithFormStatus(
              [onboarding_employee],
              organizations,
            );

          onboarding_employee = onboarding_employees?.[0];

          if (
            onboarding_employee &&
            onboarding_employee?.orientation_status &&
            onboarding_employee?.orientation_status === 'Orientation Completed'
          ) {
            try {
              const mail = {
                to: onboarding_employee.email,
                subject: 'Orientation completed successfully',
                from: '',
                text: 'Hai',
                html: `
                        <html>
                              <head></head>
                              <body>
                                  <p>Dear ${onboarding_employee.name.toUpperCase()},</p>
                                  <br/>
                                  <p>Congratulations! You have completed the orientation process. </p>
                                  <br/>
                                  ${
                                    onboarding_employee?.orientationResultString
                                  }
                                  <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${organizationEmail}">${organizationEmail}</a> </p>
                                  <br/>
                                  <p>Welcome to the team!</p>
                                  <br/>
                                  <p>Best regards,</p>
                                  <p>HR Manager, </p>
                                  <p>${
                                    organizationName
                                      ? organizationName?.toUpperCase()
                                      : 'The Kleza FAB Team'
                                  } </p>
                                  </body>
                        </html>`,
              };

              const organization_id: any = user['organization'];

              await this.sendGridService.sendWithOrganizationId(
                organization_id,
                mail,
              );
            } catch (error) {
              throw new HttpException(
                error?.message ||
                  'Something went wrong. Please try again later.',
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }
          }
        }

        return form_value ? true : false;
      }
    } catch (error) {
      throw error;
    }
  }

  async saveDataWithOutValidation(
    form_id: string,
    body: any,
    user: Express.User,
    isClientRequired: boolean,
    e_sign_request_logs?: any,
  ): Promise<any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }

      let total_points: number;
      let scored_points: number;

      const form = await this.findOne(
        {
          form_id,
          organization: e_sign_request_logs
            ? e_sign_request_logs?.organization_id
            : user['organization'],
          app_id: e_sign_request_logs
            ? e_sign_request_logs?.app_id
            : user['app_id'],
          status: true,
        },
        false,
        { ...this.select, _id: true },
      );

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      if (form.is_quiz_form) {
        const scoreResults = this.calculateTotalScoreAndPoints(
          form.fields,
          body,
        );

        total_points = scoreResults?.totalPoints;
        scored_points = scoreResults?.scoredPoints;

        // const mail = {
        //   to: user['email'],
        //   subject: 'Orientation Form Score Result',
        //   from: '',
        //   text: 'Hai',
        //   html: `<html>
        //           <head>
        //           </head>
        //           <p>Dear ${user['name']},
        //           <br/>
        //           <p>Thank you for completing the orientation quiz! We’re pleased to share that you have secured <b>${scored_points}</b> out of <b>${total_points}</b> points. </p>
        //           <br/>
        //           <p>Your results give us insight into your understanding of our organization's values and processes. Whether you've scored high or see room for improvement, we're here to support your growth and success. </p>
        //           <br/>
        //           <p>Once again, welcome to the team! We’re excited to work with you and look forward to your contributions.</p>
        //           <br/>
        //           <p>Best Regards,  </p>
        //           <p>The Kleza FAB Team </p>
        //           </body>
        //         </html>`,
        // };

        // await this.sendGridService.sendWithOrganizationId(
        //   user['organization'],
        //   mail,
        // );
      }

      let client: any;

      const completed = !isObjectEmpty(body);

      let formValueFindConditions: any = {
        form: form['_id'],
        organization_id: e_sign_request_logs
          ? e_sign_request_logs?.organization_id
          : user['organization'],
      };

      if (isClientRequired) {
        client = await this.clientsRepository.findOne({
          client_id: body.client_id,
        });

        if (!client)
          throw { status: HttpStatus.NOT_FOUND, message: 'Client Not Found' };

        formValueFindConditions.client = client._id;
      } else {
        formValueFindConditions.user_id = e_sign_request_logs
          ? e_sign_request_logs?.user_id
          : user['user_id'];
      }

      const value = await this.formValueRepository.findOne(
        formValueFindConditions,
      );

      const resData =
        await this.globalService.validateAndEliminationOfUnknownValues(
          form.validationschema,
          body,
          form,
          value?.form_value_id,
          e_sign_request_logs
            ? e_sign_request_logs?.organization_id
            : user['organization'],
        );

      if (Object.keys(resData?.errors).length) {
        return resData?.errors;
      } else {
        if (form_id === process.env.HAND_BOOK_FORM_ID) {
          const formDetailsForEmail = {
            form,
            values: body,
          };

          const options = {
            format: 'A4',
            printBackground: true,
            displayHeaderFooter: true,
            footerTemplate: `
              <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                <div style="flex: 1; text-align: center;">
                 <b>Employee Onboarding</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
              </div>
            `,
            margin: {
              top: '10mm',
              right: '10mm',
              bottom: '15mm',
              left: '10mm',
            },
          };

          // Generate the HTML content
          const file = {
            content: await this.generateHtml(formDetailsForEmail),
          };

          // Generate the PDF buffer
          const pdfBuffer = await pdf.generatePdf(file, options);

          // Convert PDF buffer to base64
          const pdfBase64 = pdfBuffer.toString('base64');

          // Sanitize file name
          const fileName = `${sanitizeFileName(form.name?.trim())}.pdf`;

          // Send email with the PDF attachment
          const email: any = {
            to: user['email'],
            from: '',
            subject: 'Assured Home Nursing Handbook',
            text: 'Please find the attached PDF document.',
            attachments: [
              {
                content: pdfBase64,
                filename: `${fileName}.pdf`,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ],
          };

          await this.sendGridService.sendWithOrganizationId(
            user['organization'],
            email,
          );
        }

        let form_value: any;

        let values: any;

        let values_encryption_status: boolean = false;

        if (completed) {
          values = this.encryptionService.encryption(resData?.reqValues);
          values_encryption_status = true;
        }

        if (!form?.has_multiple_form_responses && value) {
          form_value = await this.formValueRepository.findOneAndUpdate(
            {
              _id: value._id,
            },
            {
              client: isClientRequired ? client._id : null,
              values,
              values_encryption_status,
              completed,
              total_points,
              scored_points,
              e_sign_request_logs: e_sign_request_logs
                ? e_sign_request_logs
                : null,
            },
          );
        } else {
          form_value = await this.formValueRepository.create({
            form,
            client: isClientRequired ? client._id : null,
            user_id: e_sign_request_logs
              ? e_sign_request_logs?.user_id
              : user['user_id'],
            organization_id: e_sign_request_logs
              ? e_sign_request_logs?.organization_id
              : user['organization'],
            values,
            values_encryption_status,
            completed,
            total_points,
            scored_points,
            form_value_id: generateUUID(),
            e_sign_request_logs: e_sign_request_logs
              ? e_sign_request_logs
              : null,
          });

          if (isClientRequired) {
            await this.clientsRepository.findOneAndUpdate(
              { client_id: client.client_id },
              {
                $addToSet: { form_values: form_value._id },
              },
            );
          }
        }

        if (form.is_quiz_form) {
          let onboarding_employee: any =
            await this.onboardingEmployeeService.findOne({
              onboarding_employee_id: user['user_id'],
            });

          const organizations = await this.orgService.findOne(
            {
              organization_id: user['organization'],
            },
            false,
            { id: true, name: true, email: true },
          );

          const { name: organizationName, email: organizationEmail } =
            organizations;

          delete organizations?.name;
          delete organizations?.email;

          const onboarding_employees =
            await this.onboardingEmployeeService.employeesWithFormStatus(
              [onboarding_employee],
              organizations,
            );

          onboarding_employee = onboarding_employees?.[0];

          if (
            onboarding_employee &&
            onboarding_employee?.orientation_status &&
            onboarding_employee?.orientation_status === 'Orientation Completed'
          ) {
            try {
              const mail = {
                to: onboarding_employee.email,
                subject: 'Orientation completed successfully',
                from: '',
                text: 'Hai',
                html: `
                        <html>
                              <head></head>
                              <body>
                                  <p>Dear ${onboarding_employee.name.toUpperCase()},</p>
                                  <br/>
                                  <p>Congratulations! You have completed the orientation process. </p>
                                  <br/>
                                  <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${organizationEmail}">${organizationEmail}</a> </p>
                                  <br/>
                                  <p>Welcome to the team!</p>
                                  <br/>
                                  <p>Best regards,</p>
                                  <p>HR Manager, </p>
                                  <p>${
                                    organizationName
                                      ? organizationName?.toUpperCase()
                                      : 'The Kleza FAB Team'
                                  } </p>
                                  </body>
                        </html>`,
              };

              const organization_id: any = user['organization'];

              await this.sendGridService.sendWithOrganizationId(
                organization_id,
                mail,
              );
            } catch (error) {
              throw new HttpException(
                error?.message ||
                  'Something went wrong. Please try again later.',
                error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }
          }
        }

        return form_value ? true : false;
      }
    } catch (error) {
      throw error;
    }
  }

  async saveFormResponse(
    form_id: string,
    body: any,
    user: Express.User,
    isClientRequired: boolean,
    formValidation: boolean = true,
    e_sign_request_logs?: any,
  ): Promise<any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }

      const form = await this.findOne(
        {
          form_id,
          organization: user['organization'],
          app_id: user['app_id'],
          status: true,
        },
        false,
        { ...this.select, _id: true },
      );

      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };

      let primary_form: PrimaryFormsRepository = null;

      const formValueFindConditions: QueryOptions<FormValuesRepository> = {
        form: form['_id'],
        organization_id: e_sign_request_logs
          ? e_sign_request_logs?.organization_id
          : user['organization'],
      };

      const primary_form_id = body?.primary_form_id || null;

      if (isClientRequired) {
        if (primary_form_id) {
          body?.primary_form_id && delete body?.primary_form_id;

          primary_form = await this.primaryFormsRepository.findOne({
            primary_form_id,
          });

          if (!primary_form)
            throw {
              status: HttpStatus.NOT_FOUND,
              message: 'Primary form Not Found',
            };
        } else {
          primary_form = await this.primaryFormsRepository.create({
            primary_form_id: generateUUID(),
            original_form_id: form_id,
            organization_id: user['organization'],
            app_id: user['app_id'],
            original_form: form,
          });
        }
        formValueFindConditions.primary_form = primary_form['_id'];
      } else {
        formValueFindConditions.user_id = e_sign_request_logs
          ? e_sign_request_logs?.user_id
          : user['user_id'];
      }

      const completed = !isObjectEmpty(body);

      const value = await this.formValueRepository.findOne(
        formValueFindConditions,
      );

      let errors: any;

      if (!formValidation || e_sign_request_logs) {
        const filteredFromValuesAdnErrors =
          await this.globalService.validateAndEliminationOfUnknownValues(
            form.validationschema,
            body,
            form,
            value?.form_value_id,
            e_sign_request_logs
              ? e_sign_request_logs?.organization_id
              : user['organization'],
          );
        errors = filteredFromValuesAdnErrors?.errors;
        body = filteredFromValuesAdnErrors?.reqValues;
      } else {
        errors = await this.globalService.validate(
          form.validationschema,
          body,
          form,
          value?.form_value_id,
          user['organization'],
        );
      }

      if (completed) {
        body = await this.encryptionService.encryption(body);
      }

      if (Object.keys(errors).length) {
        return errors;
      } else {
        let form_response: FormValuesRepository = null;

        if (value) {
          form_response = await this.formValueRepository.findOneAndUpdate(
            {
              _id: value._id,
            },
            {
              values: body,
              completed,
              values_encryption_status: true,
              primary_form,
              e_sign_request_logs: e_sign_request_logs
                ? e_sign_request_logs
                : null,
            },
          );
        } else {
          form_response = await this.formValueRepository.create({
            form,
            user_id: e_sign_request_logs
              ? e_sign_request_logs?.user_id
              : user['user_id'],
            organization_id: e_sign_request_logs
              ? e_sign_request_logs?.organization_id
              : user['organization'],
            values: body,
            completed,
            values_encryption_status: true,
            form_value_id: generateUUID(),
            primary_form,
          });
        }

        if (primary_form && form_response) {
          await this.primaryFormsRepository.findOneAndUpdate(
            {
              _id: primary_form['_id'],
            },
            {
              values: form_response,
            },
          );
        }

        return form_response;
      }
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  /**
   * Get the forms based on App
   * @param organization_id string
   * @param app_id string
   * @returns Promise<APP_FORMS[]>
   */
  async getAllFormsByApps(
    organization_id: string,
    app_id?: string,
  ): Promise<APP_FORMS[] | any> {
    try {
      let organization: Organization;

      if (organization_id) {
        organization = await this.orgService.findOne(
          {
            organization_id: organization_id,
          },
          false,
          { id: true, ...this.orgService.select },
          { apps: true },
        );
      }

      if (app_id) {
        let condition: FindOptionsWhere<Apps> = {
          app_id,
        };

        if (organization_id) {
          condition.organizations = { id: organization?.id };
        }

        const app = await this.appsService.findOne(condition);

        if (!app) {
          throw new NotFoundException('App Not Found');
        }

        let forms: OrgFormsRepository[] = await this.find(
          {
            app_id: app.app_id,
            is_sub_form: false,
            organization: organization_id,
          },
          false,
          {
            form_id: true,
            name: true,
            status: true,
            _id: false,
            has_sub_forms: true,
            order_position: true,
            icon: true,
          },
        );

        const toggleDetails: AppOrgFormsToggleRepository =
          await this.appOrgFormsToggleRepository.findOne({
            app_id: app.app_id,
            organization_id: organization_id,
          });

        if (toggleDetails && toggleDetails.status) {
          forms = forms.sort((a, b) => a.order_position - b.order_position);
        }

        return {
          name: app.name,
          app_id: app.app_id,
          status: app.status,
          description: app.description,
          org_app_configuration: app?.orgAppConfiguration,
          toggle_forms_fill_order_status: toggleDetails?.status || false,
          forms,
        };
      } else {
        const apps = organization.apps;
        if (apps.length === 0 || !apps) {
          throw {
            status: HttpStatus.NOT_FOUND,
            message:
              'Before coming to forms page please activate atleast one App.',
          };
        }
        const data: APP_FORMS[] = await Promise.all(
          apps.map(async (app: Apps): Promise<APP_FORMS> => {
            const forms = await this.find(
              {
                app_id: app.app_id,
                is_sub_form: false,
                organization: organization_id,
              },
              false,
              {
                form_id: true,
                name: true,
                status: true,
                _id: false,
                has_sub_forms: true,
              },
            );
            return {
              name: app.name,
              app_id: app.app_id,
              status: app.status,
              description: app.description,
              forms,
            };
          }),
        ).catch((error) => {
          throw error;
        });
        if (data.length) return data;
        throw new HttpException('Please add Apps first', HttpStatus.NOT_FOUND);
      }
    } catch (error) {
      throw error;
    }
  }

  async getFormValueByIdAndUserIdV2(
    organization: string,
    app_id: string,
    form_id: string,
    primary_form_id: string,
    user_id: string,
    isClientRequired: boolean,
  ) {
    try {
      let matchCondition: QueryOptions<FormValuesRepository> = {};

      if (isClientRequired) {
        const primary_form = await this.primaryFormsRepository.findOne({
          primary_form_id,
        });
        if (!primary_form)
          throw {
            status: HttpStatus.NOT_FOUND,
            message:
              'Primary from details not found. Please check primary_form_id',
          };
        matchCondition = { primary_form: primary_form._id };
      } else {
        matchCondition = { user_id };
      }

      const [data] = await this.OrgFormsRepository.aggregate([
        {
          $match: { form_id, organization, app_id, status: true },
        },
        {
          $lookup: {
            from: 'form_values_repository',
            localField: '_id',
            foreignField: 'form',
            as: 'value',
            pipeline: [
              {
                $match: matchCondition,
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$value',
            preserveNullAndEmptyArrays: true,
          },
        },
      ]);

      if (!data) {
        throw new NotFoundException(
          'Form details not found, Please check the form_id',
        );
      }

      const sections = data.fields;

      const sectionsKey = Object.keys(sections);

      const fieldsObj = {};

      const groups = sectionsKey.map((key: string): GROUP => {
        const fields = data.fields[key].fields.sort((f1: FIELD, f2: FIELD) =>
          f1.field_index > f2.field_index ? 1 : -1,
        );
        fieldsObj[key] = fields.map((field: FIELD) => ({
          name: field.name,
          is_iterative_or_not: field.is_iterative_or_not,
        }));
        return {
          ...data.fields[key],
          fields: fields,
          group_key: key,
          group_description: data?.fields[key]?.group_description || '',
        };
      });

      const values: any = data?.value?.values_encryption_status
        ? this.encryptionService.decryption(data?.value.values)
        : data?.value?.values;

      const formatFieldValues = (key: string) => {
        const fields = fieldsObj[key];
        fields.forEach((field: any) => {
          if (values?.[key]?.[field.name]) {
            const is_iterative_or_not = field.is_iterative_or_not;
            if (is_iterative_or_not) {
              if (!_.isArray(values[key][field.name])) {
                values[key][field.name] = [values[key][field.name]];
              } else {
                values[key][field.name] = values[key][field.name];
              }
            }
          }
        });
      };

      const formatIterativeFieldValues = (values, key) => {
        return values.map((value: any) => {
          const valueKeys = Object.keys(value);
          valueKeys.forEach((vk) => {
            const field = fieldsObj[key].filter(
              (field: any) => field.name === vk,
            );
            if (field[0] && field[0]?.is_iterative_or_not) {
              if (!_.isArray(value[vk])) {
                value[vk] = [value[vk]];
              }
            }
          });
          return value;
        });
      };

      sectionsKey.forEach((key) => {
        const is_iterative_or_not = sections[key].is_iterative_or_not;
        if (is_iterative_or_not) {
          if (values && key in values) {
            if (!_.isArray(values[key])) {
              values[key] = formatIterativeFieldValues([values[key]], key);
            } else {
              values[key] = formatIterativeFieldValues(values[key], key);
            }
          }
        } else {
          formatFieldValues(key);
        }
      });

      const responseData: any = {
        groups,
        values,
        name: data.name,
        app_id: data.app_id,
        status: data.status,
        form_id: data.form_id,
        has_sub_forms: data.has_sub_forms,
        sectionViewType: data.sectionViewType,
        tabletLayout: data.tabletLayout,
      };

      return responseData;
    } catch (error) {
      throw error;
    }
  }

  async getFormValueByIdAndUserId(
    organization: string,
    app_id: string,
    form_id: string,
    client_id: string,
    user_id: string,
    isClientRequired: boolean,
  ) {
    try {
      let matchCondition: any = {};

      if (isClientRequired) {
        const client = await this.clientsRepository.findOne({ client_id });
        if (!client)
          throw { status: HttpStatus.NOT_FOUND, message: 'Client Not Found' };
        matchCondition = { client: client._id };
      } else {
        matchCondition = { user_id };
      }

      const [data] = await this.OrgFormsRepository.aggregate([
        {
          $match: { form_id, organization, app_id, status: true },
        },
        {
          $lookup: {
            from: 'form_values_repository',
            localField: '_id',
            foreignField: 'form',
            as: 'value',
            pipeline: [
              {
                $match: matchCondition,
              },
            ],
          },
        },
        {
          $addFields: {
            value: {
              $cond: {
                if: { $eq: ['$has_multiple_form_responses', true] },
                then: '$value',
                else: { $arrayElemAt: ['$value', 0] },
              },
            },
          },
        },
      ]);

      if (!data) {
        throw new NotFoundException();
      }

      const sections = data.fields;
      const sectionsKey = Object.keys(sections);
      const fieldsObj = {};

      const groups = await Promise.all(
        sectionsKey.map(async (key: string): Promise<GROUP> => {
          const sortedFields = [...data.fields[key].fields].sort(
            (f1: FIELD, f2: FIELD) =>
              f1.field_index > f2.field_index ? 1 : -1,
          );
          const fields = await Promise.all(
            sortedFields.map(async (field: FIELD) =>
              field.type === 'clients_list'
                ? {
                    ...field,
                    options:
                      await this.globalService.getClientsForClientsListInput(
                        user_id,
                        organization,
                      ),
                  }
                : field,
            ),
          );
          fieldsObj[key] = fields.map((field: FIELD) => ({
            name: field.name,
            is_iterative_or_not: field.is_iterative_or_not,
          }));
          return {
            ...data.fields[key],
            fields: fields,
            group_key: key,
            group_description: data?.fields[key]?.group_description || '',
          };
        }),
      );

      const formatFieldValuesOnObject = (values, key: string) => {
        const fields = fieldsObj[key];
        fields.forEach((field: any) => {
          if (values?.[key]?.[field.name]) {
            const is_iterative_or_not = field.is_iterative_or_not;
            if (is_iterative_or_not) {
              if (!_.isArray(values[key][field.name])) {
                values[key][field.name] = [values[key][field.name]];
              }
            }
          }
        });
      };

      const formatIterativeFieldValues = (values, key) => {
        return values.map((value: any) => {
          const valueKeys = Object.keys(value);
          valueKeys.forEach((vk) => {
            const field = fieldsObj[key].find(
              (field: any) => field.name === vk,
            );
            if (field && field?.is_iterative_or_not) {
              if (!_.isArray(value[vk])) {
                value[vk] = [value[vk]];
              }
            }
          });
          return value;
        });
      };

      // Final list of values (either single or multiple)
      let valuesList: any[] | any = [];

      if (Array.isArray(data?.value)) {
        // Multiple form responses
        valuesList = data.value.map((v: any) => {
          const decryptedValues = v?.values_encryption_status
            ? this.encryptionService.decryption(v.values)
            : v.values;

          const formattedValues = { ...decryptedValues };

          sectionsKey.forEach((key) => {
            const is_iterative_or_not = sections[key].is_iterative_or_not;
            if (is_iterative_or_not) {
              if (formattedValues && key in formattedValues) {
                if (!_.isArray(formattedValues[key])) {
                  formattedValues[key] = formatIterativeFieldValues(
                    [formattedValues[key]],
                    key,
                  );
                } else {
                  formattedValues[key] = formatIterativeFieldValues(
                    formattedValues[key],
                    key,
                  );
                }
              }
            } else {
              formatFieldValuesOnObject(formattedValues, key);
            }
          });

          return formattedValues;
        });
      } else {
        // Single form response
        const decryptedValues = data?.value?.values_encryption_status
          ? this.encryptionService.decryption(data?.value?.values)
          : data?.value?.values;

        const formattedValues = { ...decryptedValues };

        sectionsKey.forEach((key) => {
          const is_iterative_or_not = sections[key].is_iterative_or_not;
          if (is_iterative_or_not) {
            if (formattedValues && key in formattedValues) {
              if (!_.isArray(formattedValues[key])) {
                formattedValues[key] = formatIterativeFieldValues(
                  [formattedValues[key]],
                  key,
                );
              } else {
                formattedValues[key] = formatIterativeFieldValues(
                  formattedValues[key],
                  key,
                );
              }
            }
          } else {
            formatFieldValuesOnObject(formattedValues, key);
          }
        });

        valuesList = formattedValues;
      }

      const responseData: any = {
        groups,
        values: valuesList,
        name: data.name,
        icon: data?.icon,
        app_id: data.app_id,
        status: data.status,
        form_id: data.form_id,
        has_sub_forms: data.has_sub_forms,
        is_quiz_form: data.is_quiz_form,
        sectionViewType: data.sectionViewType,
        tabletLayout: data.tabletLayout,
        is_disable_form_response: data?.is_disable_form_response,
        has_multiple_form_responses: data?.has_multiple_form_responses,
      };

      return responseData;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Change status of form
   * @param id number
   */
  async changeStatus(id: string, body: any) {
    try {
      const form = await this.findOne({ form_id: id });

      const fields = form.fields;
      let auto_fill_dependencies: boolean;
      for (const groupKey in fields) {
        if (fields.hasOwnProperty(groupKey)) {
          const group = fields[groupKey];

          for (const field of group.fields) {
            if (field?.auto_fill_dependencies) {
              auto_fill_dependencies = true;
            }
          }
        }
      }

      if (auto_fill_dependencies && form.status) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'This form fields are dependent on another form field and cannot be unpublish.',
        };
      }
      if (form) {
        return await this.OrgFormsRepository.findOneAndUpdate(
          {
            form_id: form.form_id,
          },
          body,
          {
            returnDocument: 'after',
          },
        );
      }
      throw new NotFoundException();
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong. Please try again later.',
        error.status || 500,
      );
    }
  }

  /**
   * Delete the field in form by field id and group key
   * @param form_id string
   * @param body DeleteFiedlDto
   * @returns Promise<boolean>
   */
  async deleteField(
    form_id: string,
    body: DeleteFieldDto,
    organization: string,
  ): Promise<boolean> {
    try {
      const form = await this.findOne({ form_id });

      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };

      const group_key = body.group_key;

      const field_id = body.field_id;

      if (field_id) {
        const key = `fields.${group_key}.fields`;

        const form = await this.findFieldInFormWithIds(
          organization,
          form_id,
          field_id,
          group_key,
        );

        if (!form)
          throw {
            status: HttpStatus.NOT_FOUND,
            message: 'Form/Field Not Found',
          };

        const dependencies = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].dependencies;

        const autoFillDep = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].auto_fill_dependencies;

        if (dependencies || autoFillDep) {
          throw {
            status: HttpStatus.BAD_REQUEST,
            message:
              'This form field are dependent on another form or this form field and cannot be deleted.',
          };
        }

        const field_key = form.fields[group_key].fields.filter(
          (field: FIELD) => field.field_id === field_id,
        )[0].name;

        const validationschema = form.validationschema;

        delete validationschema[group_key][field_key];

        return (await this.OrgFormsRepository.findOneAndUpdate(
          { form_id },
          {
            $pull: {
              [key]: { field_id },
            },
            validationschema,
          },
          {
            new: true,
          },
        ).catch((error) => {
          throw error;
        }))
          ? true
          : false;
      }

      let auto_fill_dependencies: boolean;
      let field_dependencies: boolean;
      for (const field of form.fields[group_key].fields) {
        if (field?.auto_fill_dependencies) {
          auto_fill_dependencies = true;
        }
        if (field?.dependencies) {
          field_dependencies = true;
        }
      }
      if (auto_fill_dependencies || field_dependencies) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message:
            'This form section fields are dependent on another form or this form field and cannot be deleted.',
        };
      }

      const key = `fields.${group_key}`;

      const validationKey = `validationschema.${group_key}`;

      return (await this.OrgFormsRepository.findOneAndUpdate(
        { form_id },
        {
          $unset: { [key]: null, [validationKey]: null },
        },
      ))
        ? true
        : false;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate Preview form
   * @param form_id string
   * @param body any
   * @returns Promise<boolean | any>
   */
  async validatePreview(form_id: any, body: any): Promise<boolean | any> {
    try {
      if (Object.keys(body)?.length === 0) {
        throw {
          status: HttpStatus.BAD_REQUEST,
          message: 'Please send the values',
        };
      }
      const form = await this.findOne({ form_id });
      if (!form)
        throw { status: HttpStatus.NOT_FOUND, message: 'Form Not Found' };
      const errors = await this.globalService.validate(
        form.validationschema,
        body,
        form,
      );
      return Object.keys(errors).length ? errors : true;
    } catch (error) {
      throw {
        status: HttpStatus.BAD_REQUEST,
        message: 'Please send the values in correct format',
      };
    }
  }

  /**
   *
   * @param form_id string
   * @param body UpdateIndex
   * @returns Promise<OrgFormsRepository>
   */
  async updateIndex(
    form_id: string,
    body: UpdateIndex,
  ): Promise<OrgFormsRepository> {
    try {
      const form = await this.findOne({ form_id });
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      const group_key = body.group_key;
      const fields = body.fields;
      const group_index = body.group_index;
      const updatedaData = {};
      if (group_index) {
        updatedaData[`fields.${group_key}.group_index`] = group_index;
      }
      if (fields?.length) {
        const fieldIds = fields.map((field) => field.field_id);
        const fieldIndexs = fields.map((field) => field.field_index);
        const form_fields = form.fields[group_key].fields.map((field) => {
          if (fieldIds.includes(field.field_id)) {
            return {
              ...field,
              field_index:
                fieldIndexs[fieldIds.findIndex((id) => id === field.field_id)],
            };
          }
          return field;
        });
        updatedaData[`fields.${group_key}.fields`] = form_fields;
      }
      return await this.OrgFormsRepository.findOneAndUpdate(
        { form_id },
        updatedaData,
        { new: true },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param form_id string
   * @param body UpdateIndex
   * @returns Promise<OrgFormsRepository>
   */
  async updateGroupsIndex(
    form_id: string,
    body: UpdateGroupIndex,
  ): Promise<OrgFormsRepository> {
    try {
      const form = await this.findOne({ form_id });

      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };

      const updatedData = { fields: {} };

      const groupKeys = body.groups.map((group: any) => group.group_key);

      const groupIndexes = body.groups.map((group: any) => group.group_index);

      groupKeys.map((key, index) => {
        updatedData['fields'][key] = {
          ...form.fields[key],
          group_index: groupIndexes[index],
        };
      });

      return await this.OrgFormsRepository.findOneAndUpdate(
        { form_id },
        updatedData,
        { new: true },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
    } catch (error) {
      throw error;
    }
  }

  /**
   *
   * @param body UpdateFormsIndex
   * @returns Promise<any>
   */
  async updateFormsIndex(body: UpdateFormsIndex): Promise<any> {
    try {
      return await Promise.all(
        body.forms.map(async (formIndex: FormIndex) => {
          const form = await this.findOne({ form_id: formIndex.form_id });

          if (!form)
            throw {
              status: HttpStatus.NOT_FOUND,
              message: 'Form/Field Not Found',
            };

          return await this.OrgFormsRepository.findOneAndUpdate(
            { form_id: form.form_id },
            { order_position: formIndex.form_index },
            {
              returnDocument: 'after',
            },
          );
        }),
      );
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   *
   * @param app_id String
   * @param organization_id String
   * @returns Promise<any>
   */
  async toggleFormsFillOrder(
    app_id: string,
    organization_id: string,
    form_id?: string,
  ): Promise<any> {
    try {
      const toggleDetails: AppOrgFormsToggleRepository =
        await this.appOrgFormsToggleRepository.findOne({
          app_id,
          organization_id,
          form_id,
        });

      if (!toggleDetails) {
        return await this.appOrgFormsToggleRepository.create({
          app_id,
          organization_id,
          form_id,
          status: true,
        });
      } else {
        return await this.appOrgFormsToggleRepository.findOneAndUpdate(
          { app_id, organization_id, form_id },
          { status: !toggleDetails.status },
          { new: true },
        );
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Copy the field based on requested copy details
   * @param form_id string
   * @param body CopyFieldDto
   * @returns Promise<FIELD>
   */
  async copyField(
    organization: string,
    form_id: string,
    body: CopyFieldDto,
  ): Promise<FIELD> {
    try {
      const field_id = body.field_id;
      const group_key = body.group_key;
      const form: OrgFormsRepository = await this.findFieldInFormWithIds(
        organization,
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Field was not found in given section or form',
        };
      const copy_field: FIELD = form.fields[group_key].fields.filter(
        (field: FIELD) => field.field_id === field_id,
      )[0];
      copy_field.name = `${copy_field.name}_copy_${Date.now()}`;
      copy_field.field_id = generateUUID();
      const key = `fields.${group_key}.fields`;
      const data = await this.OrgFormsRepository.findOneAndUpdate(
        { form_id: form.form_id },
        {
          $push: {
            [key]: copy_field,
          },
          validationschema: {
            ...form.validationschema,
            [group_key]: {
              ...form.validationschema[group_key],
              [copy_field.name]: {
                type: copy_field?.input_type || 'text',
                ...copy_field.validation_schema,
              },
            },
          },
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (data) {
        return copy_field;
      }
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to copy the field',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Copy or Duplicate the section with form
   * @param form_id string
   * @param body any
   * @returns Promise<FIELD>
   */
  async duplicateSection(
    form_id: string,
    body: any,
  ): Promise<OrgFormsRepository> {
    try {
      const group_key = body.group_key;
      const group_index = body.group_index;

      const form: OrgFormsRepository = await this.findOne({ form_id }, false, {
        form_id: true,
        fields: true,
        status: true,
        has_sub_forms: true,
        validationschema: true,
      });
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Field was not found in given section or form',
        };

      let newKey = group_key;
      let counter = 1;

      while (form.fields.hasOwnProperty(newKey)) {
        newKey = `${group_key}_copy${counter}`;
        counter++;
      }

      const sectionFields = [];

      let duplicateField = {
        group_title: form.fields[group_key].group_title,
        group_key: newKey,
        group_id: generateUUID(),
        group_description: form.fields[group_key].group_description,
        group_index: group_index,
        is_iterative_or_not: form.fields[group_key]?.is_iterative_or_not,
        iteration_min_length: form.fields[group_key]?.iteration_min_length,
        iteration_max_length: form.fields[group_key]?.iteration_max_length,
        fields: form.fields[group_key].fields.map((field: FIELD) => {
          const newFelidName = `${field.name}_copy_${Date.now()}`;

          sectionFields.push({
            newFelidName,
            oldFelidName: field.name,
          });

          field.name = newFelidName;
          field.field_id = generateUUID();
          return field;
        }),
      };

      let duplicateSectionValidation = {};

      sectionFields.map((field) => {
        duplicateSectionValidation[field.newFelidName] =
          form.validationschema[group_key][field.oldFelidName];
      });

      const OrgFormsRepository: OrgFormsRepository = await this.findOne(
        { form_id },
        false,
        {
          form_id: true,
          fields: true,
          status: true,
          has_sub_forms: true,
          validationschema: true,
        },
      );

      const data = await this.OrgFormsRepository.findOneAndUpdate(
        { form_id: form.form_id },
        {
          fields: {
            ...OrgFormsRepository.fields,
            [newKey]: duplicateField,
          },
          validationschema: {
            ...form.validationschema,
            [newKey]: duplicateSectionValidation,
          },
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get the signature form list
   * @param response Response
   * @returns Promise<Response>
   */
  async getSignatureFormsList(
    app_code: string,
    organization_id: string,
    client_id: string,
  ) {
    try {
      const client_details: any = await this.clientsRepositoryService.findOne(
        {
          client_id,
        },
        false,
        { _id: true },
      );

      if (!client_details) {
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Invalid client_id. Please send valid one',
        };
      }

      const app_details = await this.appsService.findOne({
        app_code,
      });

      if (!app_details) {
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Invalid kleza-app-code. Please send valid one',
        };
      }

      const organization_details = await this.orgService.findOne({
        organization_id,
      });

      if (!organization_details) {
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Invalid organization_id. Please send valid one',
        };
      }

      const forms: any = await this.find(
        {
          app_id: app_details.app_id,
          organization: organization_id,
          status: true,
        },
        false,
        { ...this.select, _id: true },
        ['main_form_id'],
      );

      const signatureForms = await Promise.all(
        forms
          .filter((form: any) => {
            const hasSignatureField =
              form?.fields &&
              Object.values(form.fields).some(
                (section: any) =>
                  section?.fields &&
                  (section?.fields as FIELD[]).some(
                    (field) => field.type === INPUTS.signature,
                  ),
              );
            // Ensure form is included only if `main_form_id` is either absent or its status is true
            const isValidMainForm =
              !form?.main_form_id || form?.main_form_id?.status === true;
            return hasSignatureField && isValidMainForm;
          })
          .map(async (form: any) => {
            const checkFormValue = await this.formValueRepository.findOne({
              form: form._id,
              client: client_details._id,
              organization_id,
            });

            return {
              form_id: form.form_id,
              name: form.name,
              completed:
                checkFormValue && checkFormValue?.completed ? true : false,
            };
          }),
      );

      return signatureForms;
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get the signature form list
   * @param response Response
   * @returns Promise<Response>
   */
  async getSignatureFormsListPublic(request_id: string) {
    try {
      const e_sign_request_log = await this.eSignRequestLogsRepository.findOne({
        e_sign_request_logs_id: request_id,
      });

      if (!e_sign_request_log) {
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Invalid Request Id',
        };
      }

      if (moment().unix() > e_sign_request_log.expiry_timestamp) {
        throw {
          status: HttpStatus.REQUEST_TIMEOUT,
          message: 'E-Sign Request link expired',
        };
      }

      const organizations = await this.orgService.findOne(
        {
          organization_id: e_sign_request_log.organization_id,
        },
        false,
        { id: true },
      );

      const app_details = await this.appsService.findOne({
        app_id: e_sign_request_log.app_id,
        organizations,
      });

      const forms = await this.find(
        {
          form_id: { $in: e_sign_request_log.forms },
        },
        false,
        { ...this.select, _id: true },
        ['main_form_id'],
      );

      const signatureForms = await Promise.all(
        forms
          .filter((form: any) => {
            const hasSignatureField =
              form?.fields &&
              Object.values(form.fields).some(
                (section: any) =>
                  section?.fields &&
                  (section?.fields as FIELD[]).some(
                    (field) => field.type === INPUTS.signature,
                  ),
              );
            // Ensure form is included only if `main_form_id` is either absent or its status is true
            const isValidMainForm =
              !form?.main_form_id || form?.main_form_id?.status === true;
            return hasSignatureField && isValidMainForm;
          })
          .map(async (form: any) => {
            const client: any = await this.clientsRepositoryService.findOne(
              {
                client_id: e_sign_request_log.client_id,
              },
              false,
              { _id: true },
            );

            const checkFormValue = await this.formValueRepository.findOne({
              form: form._id,
              client: client._id,
              organization_id: e_sign_request_log.organization_id,
            });

            return {
              form_id: form.form_id,
              name: form.name,
              completed:
                checkFormValue && checkFormValue?.completed ? true : false,
            };
          }),
      );

      return { forms: signatureForms, app_details };
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async generateHtmlOld(
    client_forms_value: any,
    customOrgAppConfig: OrgAppConfiguration = null,
  ): Promise<string> {
    try {
      // Determine the logo to use, either from the custom configuration or a default value
      const logo =
        customOrgAppConfig?.logo && customOrgAppConfig.logo !== ''
          ? customOrgAppConfig.logo
          : 'https://app.klezafab.com/assets/fab-logo.png';

      // Start generating the HTML string
      let html = `
        <html>
          <head>
            <style>
              body {
              font-family: Arial, sans-serif;
            }
            .header {
              padding-bottom: 10px;
              border-bottom: 2px solid #000;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
            }
            .logo-container {
              position: absolute;
              left: 0;
            }
            .header img {
              width: 150px;
              height: 50px;
              object-fit: contain;
              margin-right: 20px;
            }
            .header h2 {
              margin: 0;
              font-size: 24px;
            }
            .content {
              margin-top: 20px;
            }
            .section-name {
              margin: 20px 0 10px 0;
              font-size: 20px;
            }
            .section-description {
              margin: 0 0 10px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
            .field {
              margin-bottom: 15px;
              margin-left: 20px;
              display: flex;
                align-items: center;
            }
            .field-name {
              margin: 0 10px 0 0;
              font-size: 16px;
              min-width: 250px;
            }
            .field-description {
              margin: 0 0 5px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
              .field-value {
                margin-left: 10px;
              }
              .signature-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 10px;
                text-align: center;
                height: 50px;
                width: 300px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .signature-field img {
                height: 50px;
                width: 300px;
              }
              .signature-field .signature-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
            }
              .scribble-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 20px;
                text-align: center;
                width: 400px;
                height: auto;
                max-height: 100%;
              display: flex;
                justify-content: center;
              align-items: center;
              }
              .scribble-field img {
                width: 400px;
                height: auto;
                max-height: 100%;
              }
              .scribble-field .scribble-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
              }
              .signature-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              }
              .signatures-container {
                margin-left: 20px;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
              }
              .signature-block {
                width: 100%;
                margin-bottom: 20px;
              }
              .signature-block.side-by-side {
                width: 48%;
              }
              .checkbox-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 10px;
              }
              .checkbox-item {
                display: flex;
                align-items: center;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="logo-container">
                <img src=${logo} width="150px" height="30px" alt="Kleza FAB">
              </div>
              <div class="title-container">
                <h2>${client_forms_value.form?.name.toUpperCase()}</h2>
              </div>
            </div>
            <div class="content">
      `;

      // Iterate through each section in the form
      for (const sectionKey in client_forms_value.form?.fields) {
        if (client_forms_value.form?.fields.hasOwnProperty(sectionKey)) {
          const section = client_forms_value.form?.fields[sectionKey];
          html += `
            <div class="section-container pb-4">
              <h4 class="section-name">${section?.group_title}</h4>
              ${
                section.group_description
                  ? `<p class="section-description">${section.group_description}</p>`
                  : ''
              }
          `;

          let signatureBuffer = [];

          // Iterate through each field within the section
          section.fields
            .sort(
              (firstField: any, secondField: any) =>
                firstField?.field_index - secondField?.field_index,
            )
            .forEach((field: any, index: number) => {
              const fieldValue =
                client_forms_value?.values?.[sectionKey]?.[field.name] || '';

              if (field.type === 'signature') {
                signatureBuffer.push({ field, sectionKey, fieldValue });

                // If we have two signatures or it's the last field, render the signatures
                if (
                  signatureBuffer.length === 2 ||
                  index === section.fields.length - 1
                ) {
                  html += `<div class="signatures-container">`;
                  signatureBuffer.forEach((sig, sigIndex) => {
                    html += `
                    <div class="signature-block ${
                      signatureBuffer.length === 2 ? 'side-by-side' : ''
                    }">
                      <div class="terms-and-conditions" style="width:100%;">
                        <p>
                          <input type="checkbox" ${
                            client_forms_value?.values?.[sig.sectionKey]?.[
                              sig.field.name + '_agree'
                            ] == true
                              ? 'checked'
                              : 'checked'
                          } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
                        </p>
                      </div>
                      <div class="signature-container" style="break-inside: avoid;!important;">
                        <div class="signature-field" style="break-inside: avoid;!important;">
                          ${
                            sig.fieldValue
                              ? `<img src="${sig.fieldValue}" alt="${sig.field.label}">`
                              : `<div class="signature-line"></div>`
                          }
                        </div>
                        <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                          sig.field.label
                        }</h4>
                        <div style="display:flex;">
                          <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                          <div class="field-value" style="margin-top: 5px!important;">${
                            (client_forms_value?.values?.[sig.sectionKey]?.[
                              sig.field.name + '_date'
                            ] &&
                              moment(
                                client_forms_value?.values?.[sig.sectionKey]?.[
                                  sig.field.name + '_date'
                                ],
                              ).format('MMMM D, YYYY, h:mm A')) ||
                            ''
                          }</div>
                        </div>
                      </div>
                    </div>
                  `;
                  });
                  html += `</div>`;
                  signatureBuffer = [];
                }
              } else if (
                field.type === 'input' &&
                field.input_type === 'checkbox'
              ) {
                if (field.label) {
                  html += `
                  <div class="field">
                    <h4 class="field-name">${field.label}</h4>
                    <div class="field-value">
                      <div class="checkbox-grid">`;

                  field.options.forEach((option: any) => {
                    const isChecked = fieldValue.includes(option.value)
                      ? 'checked'
                      : '';
                    html += `
                    <div class="checkbox-item">
                      <input type="checkbox" ${isChecked} />
                      <span style="margin-left: 5px;">${option.label}</span>
                    </div>`;
                  });

                  html += `</div></div></div>`; // Close field and checkbox grid divs
                } else {
                  // No label, display checkboxes side by side
                  html += `
                  <div class="field">
                    <div class="field-value checkbox-grid">`;

                  field.options.forEach((option: any) => {
                    const isChecked = fieldValue.includes(option.value)
                      ? 'checked'
                      : '';
                    html += `
                    <div class="checkbox-item">
                      <input type="checkbox" ${isChecked} />
                      <span>${option.label}</span>
                    </div>`;
                  });

                  html += `</div></div>`; // Close field and checkbox grid divs
                }
              } else {
                // If there's a pending signature, render it before the next non-signature field
                if (signatureBuffer.length > 0) {
                  html += `<div class="signatures-container">`;
                  signatureBuffer.forEach((sig) => {
                    html += `
                    <div class="signature-block">
                      <div class="terms-and-conditions" style="width:100%;">
                        <p>
                          <input type="checkbox" ${
                            client_forms_value?.values?.[sig.sectionKey]?.[
                              sig.field.name + '_agree'
                            ] == true
                              ? 'checked'
                              : 'checked'
                          } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
                        </p>
                      </div>
                      <div class="signature-container" style="break-inside: avoid;!important">
                        <div class="signature-field" style="break-inside: avoid;!important">
                          ${
                            sig.fieldValue
                              ? `<img src="${sig.fieldValue}" alt="${sig.field.label}">`
                              : `<div class="signature-line"></div>`
                          }
                        </div>
                        <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                          sig.field.label
                        }</h4>
                        <div style="display:flex;">
                          <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                          <div class="field-value" style="margin-top: 5px!important;">${
                            (client_forms_value?.values?.[sig.sectionKey]?.[
                              sig.field.name + '_date'
                            ] &&
                              moment(
                                client_forms_value?.values?.[sig.sectionKey]?.[
                                  sig.field.name + '_date'
                                ],
                              ).format('MMMM D, YYYY, h:mm A')) ||
                            ''
                          }</div>
                        </div>
                      </div>
                    </div>
                  `;
                  });
                  html += `</div>`;
                  signatureBuffer = [];
                }

                // Render non-signature fields
                html += `
                <div class="field" ${
                  ['image', 'scribble'].includes(field.type) &&
                  `style='break-inside: avoid;'`
                }>
                  <div>
                    ${
                      !['image', 'scribble'].includes(field.type)
                        ? `<h4 class="field-name" style="${
                            field.type !== 'paragraph'
                              ? 'max-width: 250px;'
                              : 'width: 100%;'
                          }">${field.label}</h4>`
                        : ''
                    }
  
                    ${
                      field.description
                        ? `<div class="field-description" style="${
                            field.type !== 'paragraph'
                              ? 'max-width: 250px;'
                              : 'width: 100%;'
                          }">${field.description}</div>`
                        : ''
                    }
                  </div>
              `;

                if (['scribble', 'image'].includes(field.type) && fieldValue) {
                  html += `
                  <div class="signature-container">
                    <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                      field.label
                    }</h4>  
                    <div class="scribble-field">
                      ${
                        fieldValue
                          ? `<img src="${fieldValue}" alt="${field.label}">`
                          : `<div class="scribble-line"></div>`
                      }
                    </div>
                  </div>`;
                } else if (
                  !['file', 'image', 'paragraph', 'scribble'].includes(
                    field.type,
                  )
                ) {
                  if (Array.isArray(fieldValue)) {
                    html += `<div>`;
                    fieldValue.forEach((data: any) => {
                      html += `<div class="field-value" style="text-transform: capitalize;">${data}</div>`;
                    });
                    html += `</div>`;
                  } else {
                    html += `<div class="field-value" style="text-transform: capitalize;">${
                      fieldValue || 'NA'
                    }</div>`;
                  }
                } else if (field.type === 'paragraph') {
                  html += `<div class="field-value" style="text-transform: capitalize;">${fieldValue}</div>`;
                }

                html += `</div>`; // Close .field div
              }
            });

          html += `</div>`; // Close .section-container div
        }
      }

      html += `</div></body></html>`; // Close content and HTML structure

      return html;
    } catch (error) {
      console.error('Error generating HTML:', error);
    }
  }

  async generateHtml1(
    client_forms_value: any,
    customOrgAppConfig: OrgAppConfiguration = null,
  ): Promise<string> {
    try {
      // Start generating the HTML string
      let html = `
        <html>
          <head>
            <style media="all">
            div::after {
              content: '';
              clear: both;
              display: table;
            }
            body {
              font-family: Arial, sans-serif;
            }
            .header {
              padding-bottom: 10px;
              border-bottom: 2px solid #000;
              display: flex;
              align-items: center;
              gap: 30px;
            }
            .title-container {
              flex-grow: 1;
            }
            .title-container h2 {
              word-wrap:anywhere;
              margin: 0;
              font-size: 24px;
            }
            .header img {
              width: 150px;
              height: 50px;
              object-fit: contain;
            }
            .content {
              margin-top: 20px;
            }
            .section-name {
              margin: 20px 0 10px 0;
              font-size: 20px;
            }
            .section-description {
              margin: 0 0 10px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
            .field {
              margin-bottom: 15px;
              margin-left: 20px;
              display: flex;
                align-items: center;
                width: 100%
            }
            .field-name {
              margin: 0 10px 0 0;
              font-size: 16px;
              min-width: 250px;
            }
            .field-description {
              margin: 0 0 5px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
              .field-value {
                margin-left: 10px;
              }
              .signature-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 10px;
                text-align: center;
                height: 50px;
                width: 300px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .signature-field img {
                height: 50px;
                width: 300px;
              }
              .signature-field .signature-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
              }
              .scribble-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 20px;
                text-align: center;
                width: 400px;
                height: auto;
                max-height: 100%;
              display: flex;
                justify-content: center;
              align-items: center;
              }
              .scribble-field img {
                width: 400px;
                height: auto;
                max-height: 100%;
              }
              .scribble-field .scribble-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
              }
              .signature-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              }
              .signatures-container {
                gap: 20;
                display: flex;
                justify-content: space-between;
              }
              .signature-block {
                width: auto;
                margin: 0px 20px 20px 20px;
                float: left;
              }
              .signature-block.side-by-side {
                width: 48%;
              }
              .signature-float-block {
                width: calc(50% - 40px) !important;
              }
              .checkbox-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 10px;
              }
              .checkbox-item {
                display: flex;
                align-items: center;
              }
            </style>
          </head>
          <body>
            
            <div class="content">
      `;

      var signatureCount = 0;

      //Render the section html string
      const renderSection = (
        sectionKey: string,
        section: GROUP,
        sectionResponse: any,
        sectionIndex: number = undefined,
      ) => {
        let sectionHtml = `
          <div class="section-container pb-4">
            <h4 class="section-name">${section?.group_title}${
          sectionIndex != undefined ? `- #` + (sectionIndex + +1) : ''
        }</h4>
            ${
              section.group_description
                ? `<p class="section-description">${section.group_description}</p>`
                : ''
            }
        `;

        // Iterate through each field within the section
        const fields = section.fields.sort(
          (firstField: any, secondField: any) =>
            firstField?.field_index - secondField?.field_index,
        );
        const fieldsLength = fields.length;
        fields.forEach((field: FIELD, fieldIndex: number) => {
          // next field
          const nextField =
            fieldIndex < fieldsLength - 1 ? fields[fieldIndex + 1] : null;
          // set the signature count
          if (field.type === 'signature') {
            signatureCount++;
          }
          //Check Field Iterative or not
          if (field.is_iterative_or_not) {
            const fieldResponseArray: any[] = Array.isArray(
              sectionResponse?.[field?.name],
            )
              ? sectionResponse?.[field?.name]
              : [sectionResponse?.[field?.name]];
            fieldResponseArray.map((fieldResponse, fieldIndex) => {
              // next field
              const nextArrayField =
                fieldIndex < fieldResponseArray.length - 1
                  ? fieldResponseArray[fieldIndex + 1]
                  : null;
              // Iterate through each field within the section
              sectionHtml += renderField(
                sectionKey,
                section,
                field,
                signatureCount,
                fieldResponse,
                fieldIndex,
                sectionIndex,
                '',
                nextArrayField,
              );
            });
          } else {
            const fieldResponse: any = sectionResponse?.[field?.name];
            //Render Field
            sectionHtml += renderField(
              sectionKey,
              section,
              field,
              signatureCount,
              fieldResponse,
              undefined,
              sectionIndex,
              '',
              nextField,
            );
          }
        });

        sectionHtml += `</div>`; // Close .section-container div

        return sectionHtml;
      };

      //Render the section field string
      const renderField = (
        sectionKey: any,
        section: GROUP,
        field: FIELD,
        signaturecount: number,
        fieldResponse: any,
        fieldIndex: number = undefined,
        sectionIndex: number = undefined,
        fieldHtml = '',
        nextField: FIELD = null,
      ) => {
        if (field.type === 'signature') {
          const getSignatureClassname = () => {
            const isEven = isEvenNumber(signaturecount);
            if (nextField?.type !== 'signature') {
              signatureCount = 0;
            }
            if (isEven) {
              return 'signature-float-block';
            }
            if (!isEven && nextField?.type === 'signature') {
              return 'signature-float-block';
            }
            return '';
          };

          const date =
            sectionIndex !== undefined && fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_date'
                ]?.[fieldIndex]
              : fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_date'
                ]?.[fieldIndex]
              : sectionIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_date'
                ]
              : client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_date'
                ];

          const agree =
            sectionIndex !== undefined && fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_agree'
                ]?.[fieldIndex]
              : fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_agree'
                ]?.[fieldIndex]
              : sectionIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_agree'
                ]
              : client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_agree'
                ];

          fieldHtml += ` <div class="signature-block ${getSignatureClassname()}">
            <div class="terms-and-conditions" style="width:100%;">
              <p>
                <input type="checkbox" ${
                  agree == true ? 'checked' : ''
                } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
              </p>
            </div>
            <div class="signature-container" style="break-inside: avoid;!important">
              <div class="signature-field" style="break-inside: avoid;!important">
                ${
                  fieldResponse
                    ? `<img src="${fieldResponse}" alt="${field?.label}">`
                    : `<div class="signature-line"></div>`
                }
              </div>
              <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                field?.label
              }</h4>
              <div style="display:flex;">
                <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                <div class="field-value" style="margin-top: 5px!important;">${
                  (date && moment(date).format('MMMM D, YYYY')) || ''
                }</div>
              </div>
            </div>
          </div>`;
        } else if (field.type === 'input' && field.input_type === 'checkbox') {
          if (field?.label) {
            fieldHtml += `
          <div class="field">
            <h4 class="field-name">${field?.label}</h4>
            <div class="field-value">
              <div class="checkbox-grid">`;

            field.options.forEach((option: any) => {
              const isChecked = fieldResponse.includes(option.value)
                ? 'checked'
                : '';
              fieldHtml += `
            <div class="checkbox-item">
              <input type="checkbox" ${isChecked} />
              <span style="margin-left: 5px;">${option.label}</span>
            </div>`;
            });

            fieldHtml += `
              </div>
            </div>
          </div>`; // Close field and checkbox grid divs
          } else {
            // No label, display checkboxes side by side
            fieldHtml += `
            <div class="field">
              <div class="field-value checkbox-grid">`;
            field.options.forEach((option: any) => {
              const isChecked = fieldResponse.includes(option.value)
                ? 'checked'
                : '';
              fieldHtml += `
                <div class="checkbox-item">
                  <input type="checkbox" ${isChecked} />
                  <span>${option.label}</span>
                </div>`;
            });
            fieldHtml += `
              </div>
            </div>`; // Close field and checkbox grid divs
          }
        } else {
          // If there's a pending signature, render it before the next non-signature field
          /* if (signatureBuffer.length > 0 && field?.type !== 'download') {
            fieldHtml += `<div class="signatures-container">`;
            signatureBuffer.forEach((sig) => {
              fieldHtml += `
            <div class="signature-block">
              <div class="terms-and-conditions" style="width:100%;">
                <p>
                  <input type="checkbox" ${
                    client_forms_value?.values?.[sig.sectionKey]?.[
                      sig.field.name + '_agree'
                    ] == true
                      ? 'checked'
                      : 'checked'
                  } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
                </p>
              </div>
              <div class="signature-container" style="break-inside: avoid;!important">
                <div class="signature-field" style="break-inside: avoid;!important">
                  ${
                    sig.fieldValue
                      ? `<img src="${sig.fieldValue}" alt="${sig.field?.label}">`
                      : `<div class="signature-line"></div>`
                  }
                </div>
                <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                  sig.field?.label
                }</h4>
                <div style="display:flex;">
                  <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                  <div class="field-value" style="margin-top: 5px!important;">${
                    (client_forms_value?.values?.[sig.sectionKey]?.[
                      sig.field.name + '_date'
                    ] &&
                      moment(
                        client_forms_value?.values?.[sig.sectionKey]?.[
                          sig.field.name + '_date'
                        ],
                      ).format('MMMM D, YYYY')) ||
                    ''
                  }</div>
                </div>
              </div>
            </div>
          `;
            });
            fieldHtml += `</div>`;
            signatureBuffer = [];
          } */

          // Render non-signature fields
          fieldHtml += `
        <div class="field" ${
          ['image', 'scribble', 'download'].includes(field.type) &&
          `style='break-inside: avoid;'`
        }>
          <div>
            ${
              !['image', 'scribble', 'download'].includes(field.type)
                ? `<h4 class="field-name" style="${
                    field.type !== 'paragraph'
                      ? 'max-width: 250px;'
                      : 'width: 100%;'
                  }">${field?.label}${
                    fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''
                  }</h4>`
                : ''
            }

            ${
              field.description
                ? `<div class="field-description" style="${
                    field.type !== 'paragraph'
                      ? 'max-width: 250px;'
                      : 'width: 100%;'
                  }">${field.description}</div>`
                : ''
            }
          </div>
      `;

          if (['scribble'].includes(field.type) && fieldResponse) {
            fieldHtml += `
          <div class="signature-container">
            <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
              field?.label
            }${fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''}</h4>  
            <div class="scribble-field">
              ${
                fieldResponse
                  ? `<img src="${fieldResponse}" alt="${field?.label}">`
                  : `<div class="scribble-line"></div>`
              }
            </div>
          </div>`;
          } else if (['image'].includes(field.type) && fieldResponse) {
            fieldHtml += `
          <div class="signature-container">
            <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
              field?.label
            }${fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''}</h4>  
            <div class="scribble-field">
              ${
                fieldResponse
                  ? `<img src="${fieldResponse?.file}" alt="${fieldResponse?.name}">`
                  : `<div class="scribble-line"></div>`
              }
            </div>
          </div>`;
          } else if (
            !['file', 'image', 'paragraph', 'scribble'].includes(field.type)
          ) {
            if (Array.isArray(fieldResponse)) {
              fieldHtml += `<div>`;
              fieldResponse.forEach((data: any) => {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${data}</div>`;
              });
              fieldHtml += `</div>`;
            } else {
              if (field.input_type === 'url') {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;"><a href="${field?.value}">${field?.value}</a></div>`;
              } else if (field.type == 'download') {
                fieldHtml += ``;
              } else if (field.input_type === 'date') {
                const resDate =
                  fieldResponse && moment(fieldResponse).format('MMMM D, YYYY');
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  resDate || 'NA'
                }</div>`;
              } else if (field.input_type === 'datetime-local') {
                const resDateTime =
                  fieldResponse &&
                  moment(fieldResponse).format('MMMM D, YYYY, h:mm:ss a');
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  resDateTime || 'NA'
                }</div>`;
              } else {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  fieldResponse || 'NA'
                }</div>`;
              }
            }
          }

          fieldHtml += `</div>`; // Close .field div
        }

        return fieldHtml;
      };

      // Iterate through each section in the form
      for (const sectionKey in client_forms_value.form?.fields) {
        if (client_forms_value.form?.fields.hasOwnProperty(sectionKey)) {
          //Section Details From Form
          const section: GROUP = client_forms_value.form?.fields[sectionKey];

          //Check if section is iterative or not
          if (section?.is_iterative_or_not) {
            //Section Response From Form Response Array
            const sectionResponseArray: any[] = Array.isArray(
              client_forms_value.values?.[sectionKey],
            )
              ? client_forms_value.values?.[sectionKey]
              : [client_forms_value.values?.[sectionKey]];

            // Iterate through each section response from section from response
            sectionResponseArray &&
              sectionResponseArray.length &&
              sectionResponseArray.map(
                (sectionResponse: any, sectionIndex: number) => {
                  html += renderSection(
                    sectionKey,
                    section,
                    sectionResponse,
                    sectionIndex,
                  );
                },
              );
          } else {
            //Section Response From Form Response Array
            const sectionResponse: any =
              client_forms_value.values?.[sectionKey];

            //Render Section
            html += renderSection(sectionKey, section, sectionResponse);
          }
        }
      }

      html += `</div></body></html>`; // Close content and HTML structure

      return html;
    } catch (error) {
      console.error('Error generating HTML:', error);
    }
  }

  async generateHtml(
    client_forms_value: any,
    customOrgAppConfig: OrgAppConfiguration = null,
  ): Promise<string> {
    try {
      // Determine the logo to use, either from the custom configuration or a default value
      const base64Logo = await this.getBase64(
        'https://app.klezafab.com/assets/fab-logo.png',
      );
      const logoSrc = `data:image/png;base64,${base64Logo}`;

      const logo =
        customOrgAppConfig?.logo && customOrgAppConfig.logo !== ''
          ? customOrgAppConfig.logo
          : logoSrc;

      // Start generating the HTML string
      let html = `
        <html>
          <head>
            <style media="all">
            div::after {
              content: '';
              clear: both;
              display: table;
            }
            body {
              font-family: Arial, sans-serif;
            }
            .header {
              padding-bottom: 10px;
              border-bottom: 2px solid #000;
              display: flex;
              align-items: center;
              gap: 30px;
            }
            .title-container {
              flex-grow: 1;
            }
            .title-container h2 {
              word-wrap:anywhere;
              margin: 0;
              font-size: 24px;
            }
            .header img {
              width: 150px;
              height: 50px;
              object-fit: contain;
            }
            .content {
              margin-top: 20px;
            }
            .section-name {
              margin: 20px 0 10px 0;
              font-size: 20px;
            }
            .section-description {
              margin: 0 0 10px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
            .field {
              margin-bottom: 15px;
              margin-left: 20px;
              display: flex;
                align-items: center;
                width: 100%
            }
            .field-name {
              margin: 0 10px 0 0;
              font-size: 16px;
              min-width: 250px;
            }
            .field-description {
              margin: 0 0 5px 0;
              color: inherit;
              font-style: normal;
              font-size: inherit;
              font-family: inherit;
            }
              .field-value {
                margin-left: 10px;
              }
              .signature-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 10px;
                text-align: center;
                height: 50px;
                width: 300px;
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .signature-field img {
                height: 50px;
                width: 300px;
              }
              .signature-field .signature-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
              }
              .scribble-field {
                border: 1px solid #000;
                padding: 10px;
                margin-top: 20px;
                text-align: center;
                width: 400px;
                height: auto;
                max-height: 100%;
              display: flex;
                justify-content: center;
              align-items: center;
              }
              .scribble-field img {
                width: 400px;
                height: auto;
                max-height: 100%;
              }
              .scribble-field .scribble-line {
                border-top: 1px solid #000;
                width: 100%;
                margin-top: 10px;
              }
              .signature-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              }
              .signatures-container {
                gap: 20;
                display: flex;
                justify-content: space-between;
              }
              .signature-block {
                width: auto;
                margin: 0px 20px 20px 20px;
                float: left;
              }
              .signature-block.side-by-side {
                width: 48%;
              }
              .signature-float-block {
                width: calc(50% - 40px) !important;
              }
              .checkbox-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 10px;
              }
              .checkbox-item {
                display: flex;
                align-items: center;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div class="logo-container">
                <img src=${logo} width="150px" height="30px" alt="Kleza FAB">
              </div>
              <div class="title-container">
                <h2>${client_forms_value.form?.name.toUpperCase()}</h2>
              </div>
            </div>
            <div class="content">
      `;

      var signatureCount = 0;

      //Render the section html string
      const renderSection = (
        sectionKey: string,
        section: GROUP,
        sectionResponse: any,
        sectionIndex: number = undefined,
      ) => {
        let sectionHtml = `
          <div class="section-container pb-4">
            <h4 class="section-name">${section?.group_title}${
          sectionIndex != undefined ? `- #` + (sectionIndex + +1) : ''
        }</h4>
            ${
              section.group_description
                ? `<p class="section-description">${section.group_description}</p>`
                : ''
            }
        `;

        // Iterate through each field within the section
        const fields = section.fields.sort(
          (firstField: any, secondField: any) =>
            firstField?.field_index - secondField?.field_index,
        );
        const fieldsLength = fields.length;
        fields.forEach((field: FIELD, fieldIndex: number) => {
          // next field
          const nextField =
            fieldIndex < fieldsLength - 1 ? fields[fieldIndex + 1] : null;
          // set the signature count
          if (field.type === 'signature') {
            signatureCount++;
          }
          //Check Field Iterative or not
          if (field.is_iterative_or_not) {
            const fieldResponseArray: any[] = Array.isArray(
              sectionResponse?.[field?.name],
            )
              ? sectionResponse?.[field?.name]
              : [sectionResponse?.[field?.name]];
            fieldResponseArray.map((fieldResponse, fieldIndex) => {
              // next field
              const nextArrayField =
                fieldIndex < fieldResponseArray.length - 1
                  ? fieldResponseArray[fieldIndex + 1]
                  : null;
              // Iterate through each field within the section
              sectionHtml += renderField(
                sectionKey,
                section,
                field,
                signatureCount,
                fieldResponse,
                fieldIndex,
                sectionIndex,
                '',
                nextArrayField,
              );
            });
          } else {
            const fieldResponse: any = sectionResponse?.[field?.name];
            //Render Field
            sectionHtml += renderField(
              sectionKey,
              section,
              field,
              signatureCount,
              fieldResponse,
              undefined,
              sectionIndex,
              '',
              nextField,
            );
          }
        });

        sectionHtml += `</div>`; // Close .section-container div

        return sectionHtml;
      };

      //Render the section field string
      const renderField = (
        sectionKey: any,
        section: GROUP,
        field: FIELD,
        signaturecount: number,
        fieldResponse: any,
        fieldIndex: number = undefined,
        sectionIndex: number = undefined,
        fieldHtml = '',
        nextField: FIELD = null,
      ) => {
        if (field.type === 'signature') {
          const getSignatureClassname = () => {
            const isEven = isEvenNumber(signaturecount);
            if (nextField?.type !== 'signature') {
              signatureCount = 0;
            }
            if (isEven) {
              return 'signature-float-block';
            }
            if (!isEven && nextField?.type === 'signature') {
              return 'signature-float-block';
            }
            return '';
          };

          const date =
            sectionIndex !== undefined && fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_date'
                ]?.[fieldIndex]
              : fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_date'
                ]?.[fieldIndex]
              : sectionIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_date'
                ]
              : client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_date'
                ];

          const agree =
            sectionIndex !== undefined && fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_agree'
                ]?.[fieldIndex]
              : fieldIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_agree'
                ]?.[fieldIndex]
              : sectionIndex !== undefined
              ? client_forms_value?.values?.[sectionKey]?.[sectionIndex]?.[
                  field.name + '_agree'
                ]
              : client_forms_value?.values?.[sectionKey]?.[
                  field.name + '_agree'
                ];

          fieldHtml += ` <div class="signature-block ${getSignatureClassname()}">
            <div class="terms-and-conditions" style="width:100%;">
              <p>
                <input type="checkbox" ${
                  agree == true ? 'checked' : ''
                } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
              </p>
            </div>
            <div class="signature-container" style="break-inside: avoid;!important">
              <div class="signature-field" style="break-inside: avoid;!important">
                ${
                  fieldResponse
                    ? `<img src="${fieldResponse}" alt="${field?.label}">`
                    : `<div class="signature-line"></div>`
                }
              </div>
              <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                field?.label
              }</h4>
              <div style="display:flex;">
                <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                <div class="field-value" style="margin-top: 5px!important;">${
                  (date && moment(date).format('MMMM D, YYYY')) || ''
                }</div>
              </div>
            </div>
          </div>`;
        } else if (field.type === 'input' && field.input_type === 'checkbox') {
          if (field?.label) {
            fieldHtml += `
          <div class="field">
            <h4 class="field-name">${field?.label}</h4>
            <div class="field-value">
              <div class="checkbox-grid">`;

            field.options.forEach((option: any) => {
              const isChecked = fieldResponse.includes(option.value)
                ? 'checked'
                : '';
              fieldHtml += `
            <div class="checkbox-item">
              <input type="checkbox" ${isChecked} />
              <span style="margin-left: 5px;">${option.label}</span>
            </div>`;
            });

            fieldHtml += `
              </div>
            </div>
          </div>`; // Close field and checkbox grid divs
          } else {
            // No label, display checkboxes side by side
            fieldHtml += `
            <div class="field">
              <div class="field-value checkbox-grid">`;
            field.options.forEach((option: any) => {
              const isChecked = fieldResponse.includes(option.value)
                ? 'checked'
                : '';
              fieldHtml += `
                <div class="checkbox-item">
                  <input type="checkbox" ${isChecked} />
                  <span>${option.label}</span>
                </div>`;
            });
            fieldHtml += `
              </div>
            </div>`; // Close field and checkbox grid divs
          }
        } else {
          // If there's a pending signature, render it before the next non-signature field
          /* if (signatureBuffer.length > 0 && field?.type !== 'download') {
            fieldHtml += `<div class="signatures-container">`;
            signatureBuffer.forEach((sig) => {
              fieldHtml += `
            <div class="signature-block">
              <div class="terms-and-conditions" style="width:100%;">
                <p>
                  <input type="checkbox" ${
                    client_forms_value?.values?.[sig.sectionKey]?.[
                      sig.field.name + '_agree'
                    ] == true
                      ? 'checked'
                      : 'checked'
                  } /> I agree and understand that all electronic signatures are the legal equivalent of my manual/handwritten signature and I consent to be legally bound to this agreement. I further agree my signature on this document is as valid as if I signed the document in writing. This is to be used in conjunction with the use of electronic signatures on all forms regarding any and all future documentation with a signature requirement, should I elect to have signed electronically. Under penalty of perjury, I herewith affirm that my electronic signature, and all future electronic signatures, were signed by myself with full knowledge and consent and am legally bound to these terms and conditions.
                </p>
              </div>
              <div class="signature-container" style="break-inside: avoid;!important">
                <div class="signature-field" style="break-inside: avoid;!important">
                  ${
                    sig.fieldValue
                      ? `<img src="${sig.fieldValue}" alt="${sig.field?.label}">`
                      : `<div class="signature-line"></div>`
                  }
                </div>
                <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
                  sig.field?.label
                }</h4>
                <div style="display:flex;">
                  <h4 style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">Date : </h4>
                  <div class="field-value" style="margin-top: 5px!important;">${
                    (client_forms_value?.values?.[sig.sectionKey]?.[
                      sig.field.name + '_date'
                    ] &&
                      moment(
                        client_forms_value?.values?.[sig.sectionKey]?.[
                          sig.field.name + '_date'
                        ],
                      ).format('MMMM D, YYYY')) ||
                    ''
                  }</div>
                </div>
              </div>
            </div>
          `;
            });
            fieldHtml += `</div>`;
            signatureBuffer = [];
          } */

          // Render non-signature fields
          fieldHtml += `
        <div class="field" ${
          ['image', 'scribble', 'download'].includes(field.type) &&
          `style='break-inside: avoid;'`
        }>
          <div>
            ${
              !['image', 'scribble', 'download'].includes(field.type)
                ? `<h4 class="field-name" style="${
                    field.type !== 'paragraph'
                      ? 'max-width: 250px;'
                      : 'width: 100%;'
                  }">${field?.label}${
                    fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''
                  }</h4>`
                : ''
            }

            ${
              field.description
                ? `<div class="field-description" style="${
                    field.type !== 'paragraph'
                      ? 'max-width: 250px;'
                      : 'width: 100%;'
                  }">${field.description}</div>`
                : ''
            }
          </div>
      `;

          if (['image'].includes(field.type) && fieldResponse) {
            fieldHtml += `
          <div class="signature-container">
            <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
              field?.label
            }${fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''}</h4>  
            <div class="scribble-field">
              ${
                fieldResponse
                  ? `<img src="${fieldResponse?.file}" alt="${field?.label}">`
                  : `<div class="scribble-line"></div>`
              }
            </div>
          </div>`;
          } else if (['scribble'].includes(field.type) && fieldResponse) {
            fieldHtml += `
          <div class="signature-container">
            <h4 class="field-name" style="text-align: left!important; margin-top: 5px!important;font-size: 16px!important;">${
              field?.label
            }${fieldIndex != undefined ? `- #` + (fieldIndex + +1) : ''}</h4>  
            <div class="scribble-field">
              ${
                fieldResponse
                  ? `<img src="${fieldResponse}" alt="${field?.label}">`
                  : `<div class="scribble-line"></div>`
              }
            </div>
          </div>`;
          } else if (
            !['file', 'image', 'paragraph', 'scribble'].includes(field.type)
          ) {
            if (Array.isArray(fieldResponse)) {
              fieldHtml += `<div>`;
              fieldResponse.forEach((data: any) => {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${data}</div>`;
              });
              fieldHtml += `</div>`;
            } else {
              if (field.input_type === 'url') {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;"><a href="${field?.value}">${field?.value}</a></div>`;
              } else if (field.type == 'download') {
                fieldHtml += ``;
              } else if (field.input_type === 'date') {
                const resDate =
                  fieldResponse && moment(fieldResponse).format('MMMM D, YYYY');
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  resDate || 'NA'
                }</div>`;
              } else if (field.input_type === 'datetime-local') {
                const resDateTime =
                  fieldResponse &&
                  moment(fieldResponse).format('MMMM D, YYYY, h:mm:ss a');
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  resDateTime || 'NA'
                }</div>`;
              } else {
                fieldHtml += `<div class="field-value" style="text-transform: capitalize;">${
                  fieldResponse || 'NA'
                }</div>`;
              }
            }
          }

          fieldHtml += `</div>`; // Close .field div
        }

        return fieldHtml;
      };

      // Iterate through each section in the form
      for (const sectionKey in client_forms_value.form?.fields) {
        if (client_forms_value.form?.fields.hasOwnProperty(sectionKey)) {
          //Section Details From Form
          const section: GROUP = client_forms_value.form?.fields[sectionKey];

          //Check if section is iterative or not
          if (section?.is_iterative_or_not) {
            //Section Response From Form Response Array
            const sectionResponseArray: any[] = Array.isArray(
              client_forms_value.values?.[sectionKey],
            )
              ? client_forms_value.values?.[sectionKey]
              : [client_forms_value.values?.[sectionKey]];

            // Iterate through each section response from section from response
            sectionResponseArray.map(
              (sectionResponse: any, sectionIndex: number) => {
                html += renderSection(
                  sectionKey,
                  section,
                  sectionResponse,
                  sectionIndex,
                );
              },
            );
          } else {
            //Section Response From Form Response Array
            const sectionResponse: any =
              client_forms_value.values?.[sectionKey];

            //Render Section
            html += renderSection(sectionKey, section, sectionResponse);
          }
        }
      }

      html += `</div></body></html>`; // Close content and HTML structure

      return html;
    } catch (error) {
      console.error('Error generating HTML:', error);
    }
  }

  async getBase64(url: string): Promise<string> {
    try {
      const response = await axios.get(url, { responseType: 'arraybuffer' });
      return Buffer.from(response.data, 'binary').toString('base64');
    } catch (error) {
      console.error('Error fetching image:', error);
      return '';
    }
  }

  async printClientFormWithValues1(
    organization_id: string,
    app_id: string,
    client_id: string,
    body: any,
    response: any,
  ) {
    try {
      const client: any = await this.clientsRepositoryService.findOne(
        { client_id },
        false,
        {
          _id: true,
          name: true,
          storage_folder_id: true,
          storage_folder_name: true,
        },
      );

      if (!client) {
        throw new Error('Client not found');
      }

      //Check organization details exists or not
      const organizationDetails = await this.orgService.findOne(
        { organization_id },
        false,
        { ...this.orgService.select, id: true },
      );

      if (!organizationDetails) {
        throw new Error('Organization details not found');
      }

      //Check app details exists or not
      const appDetails = await this.appsService.findOne({ app_id }, false, {
        ...this.appsService.select,
        name: true,
        id: true,
      });

      if (!appDetails) {
        throw new Error('App details not found');
      }

      //Checking Organization - App have custom details
      const customOrgAppConfig =
        await this.orgAppConfigurationRepository.findOne({
          where: {
            app: { id: appDetails?.id },
            organization: { id: organizationDetails?.id },
          },
        });

      const findBy: QueryOptions<OrgFormsRepository> = { status: true };

      if (body?.forms && body?.forms?.length) {
        findBy.form_id = { $in: body?.forms };
      } else {
        findBy.app_id = app_id;
        findBy.organization = organization_id;
      }

      let folder_name: string;

      if (body?.path) {
        folder_name = body?.path;
      } else if (
        client?.storage_folder_name &&
        client?.storage_folder_name != ''
      ) {
        folder_name = client?.storage_folder_name;
      } else {
        folder_name = client?.name?.trim();
      }

      let forms = await this.OrgFormsRepository.find(findBy).lean();

      const condition: QueryOptions<FormValuesRepository> = {
        organization_id: organization_id,
        client: client._id,
      };

      if (forms.length > 0) {
        const formIds = forms.map((form) => form._id);
        condition.form = { $in: formIds };
      }

      const client_forms_values: FormValuesRepository[] =
        await this.formValueRepository.find(condition).populate('form');

      // let lastPDF: any = null;

      const base64Logo = await this.getBase64(
        'https://app.klezafab.com/assets/fab-logo.png',
      );
      const logoSrc = `data:image/png;base64,${base64Logo}`;

      const logo =
        customOrgAppConfig?.logo && customOrgAppConfig.logo !== ''
          ? customOrgAppConfig.logo
          : logoSrc;

      const pdfBuffers: any = (
        await Promise.all(
          client_forms_values.map(
            async (client_forms_value: FormValuesRepository, index) => {
              const options = {
                format: 'A4',
                printBackground: true,
                displayHeaderFooter: true,
                headerTemplate: `<div class="header" 
                  style="border-bottom: 2px solid #000;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative; width:100%;margin:20px;height:38px;margin-top:0px">
                <div class="logo-container" style="position: absolute;
                  left: 0;">
                  <img src=${logo} width="140px" height="28px" alt="Kleza FAB">
                </div>
                <div class="title-container" style="position: absolute;">
                  <h2 style="font-size:18px">${client_forms_value.form?.name.toUpperCase()}</h2>
                </div>
              </div>`,
                footerTemplate: `
                <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                  <div style="flex: 1; text-align: center;">
                   <b>${
                     customOrgAppConfig
                       ? customOrgAppConfig?.app_name
                       : appDetails?.name
                   }</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                  </div>
                </div>
              `,
                margin: {
                  top: '20mm',
                  right: '10mm',
                  bottom: '15mm',
                  left: '10mm',
                },
              };

              // Decrypt the values if encryption status is true
              if (client_forms_value?.values_encryption_status) {
                client_forms_value.values = this.encryptionService.decryption(
                  client_forms_value?.values,
                );
              }

              // Generate the HTML content
              const file = {
                content: await this.generateHtml(
                  client_forms_value,
                  customOrgAppConfig,
                ),
              };

              // Generate the PDF buffer
              const pdfBuffer = await pdf.generatePdf(file, options);

              // lastPDF = pdfBuffer;

              // Return the buffer and the file name
              return {
                pdfBuffer,
                fileName: `${sanitizeFileName(
                  client_forms_value.form.name?.trim(),
                )}.pdf`,
              };
            },
          ),
        )
      ).filter((item: any) => item !== null); // Filter out null values

      if (body.request_type == 'share') {
        return await this.sendClientFormsToEmail(
          organization_id,
          body,
          pdfBuffers,
          folder_name,
          response,
        );
      } else {
        for (const { pdfBuffer, fileName } of pdfBuffers) {
          const mimetype = 'application/pdf';

          try {
            await this.configurationsService.uploadToStorage(
              organization_id,
              pdfBuffer,
              client_id,
              folder_name,
              fileName,
              mimetype,
            );
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        }

        return response.status(200).json({
          status: true,
          message: 'Clients forms successfully uploaded to storage',
        });

        // return response.status(200).contentType('application/pdf').send(lastPDF);
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async printClientFormWithValues(
    organization_id: string,
    app_id: string,
    client_id: string,
    body: any,
    response: any,
  ) {
    try {
      const client: any = await this.clientsRepositoryService.findOne(
        { client_id },
        false,
        {
          _id: true,
          name: true,
          storage_folder_id: true,
          storage_folder_name: true,
        },
      );

      if (!client) {
        throw new Error('Client not found');
      }

      //Check organization details exists or not
      const organizationDetails = await this.orgService.findOne(
        { organization_id },
        false,
        { ...this.orgService.select, id: true },
      );

      if (!organizationDetails) {
        throw new Error('Organization details not found');
      }

      //Check app details exists or not
      const appDetails = await this.appsService.findOne({ app_id }, false, {
        ...this.appsService.select,
        name: true,
        id: true,
      });

      if (!appDetails) {
        throw new Error('App details not found');
      }

      //Checking Organization - App have custom details
      const customOrgAppConfig =
        await this.orgAppConfigurationRepository.findOne({
          where: {
            app: { id: appDetails?.id },
            organization: { id: organizationDetails?.id },
          },
        });

      const findBy: QueryOptions<OrgFormsRepository> = {
        // status: true,
      };

      if (body?.forms && body?.forms?.length) {
        findBy.form_id = { $in: body?.forms };
      } else {
        findBy.app_id = app_id;
        findBy.organization = organization_id;
      }

      let folder_name: string;

      if (body?.path) {
        folder_name = body?.path;
      } else if (
        client?.storage_folder_name &&
        client?.storage_folder_name != ''
      ) {
        folder_name = client?.storage_folder_name;
      } else {
        folder_name = client?.name?.trim();
      }

      let forms = await this.OrgFormsRepository.find(findBy).lean();

      const condition: QueryOptions<FormValuesRepository> = {
        organization_id: organization_id,
        client: client._id,
      };

      if (forms.length > 0) {
        const formIds = forms.map((form) => form._id);
        condition.form = { $in: formIds };
      }

      const client_forms_values: FormValuesRepository[] =
        await this.formValueRepository.find(condition).populate('form');

      const pdfBuffers: any = (
        await Promise.all(
          client_forms_values.map(
            async (client_forms_value: FormValuesRepository, index) => {
              const options = {
                format: 'A4',
                printBackground: true,
                displayHeaderFooter: true,
                headerTemplate: '<span></span>',
                footerTemplate: `
                    <div style="width: 100%;font-size: 10px; display: flex; justify-content: space-between;">
                      <div style="flex: 1; text-align: center;">
                       <b>${
                         customOrgAppConfig
                           ? customOrgAppConfig?.app_name
                           : appDetails?.name
                       }</b> - Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                      </div>
                    </div>
                  `,
                margin: {
                  top: '10mm',
                  right: '10mm',
                  bottom: '20mm',
                  left: '10mm',
                },
              };

              // Decrypt the values if encryption status is true
              if (client_forms_value?.values_encryption_status) {
                client_forms_value.values = this.encryptionService.decryption(
                  client_forms_value?.values,
                );
              }

              // Generate the HTML content
              const file = {
                content: await this.generateHtml(
                  client_forms_value,
                  customOrgAppConfig,
                ),
              };

              // Generate the PDF buffer
              const pdfBuffer = await pdf.generatePdf(file, options);

              // Return the buffer and the file name
              return {
                pdfBuffer,
                fileName: `${sanitizeFileName(
                  client_forms_value.form.name?.trim(),
                )}.pdf`,
              };
            },
          ),
        )
      ).filter((item: any) => item !== null); // Filter out null values // Filter out null values

      if (body.request_type == 'share') {
        return await this.sendClientFormsToEmail(
          organization_id,
          body,
          pdfBuffers,
          folder_name,
          response,
        );
      } else {
        for (const { pdfBuffer, fileName } of pdfBuffers) {
          const mimetype = 'application/pdf';

          try {
            await this.configurationsService.uploadToStorage(
              organization_id,
              pdfBuffer,
              client_id,
              folder_name,
              fileName,
              mimetype,
            );
          } catch (error) {
            throw new HttpException(
              error?.message || 'Something went wrong. Please try again later.',
              error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
            );
          }
        }

        return response.status(200).json({
          status: true,
          message: 'Clients forms successfully uploaded to storage',
        });

        // return response.status(200).contentType('application/pdf').send(lastPDF);
      }
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendClientFormsToEmail(
    organization_id: string,
    body: any,
    pdfBuffers: any,
    folder_name: string,
    response: any,
  ) {
    try {
      const fullPath = path.join(process.cwd(), 'assets', folder_name);

      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }

      const zipPath = path.join(process.cwd(), `assets/${folder_name}.zip`);

      const folderPath = path.join(process.cwd(), `assets/${folder_name}`);

      const zipOutPut = fs.createWriteStream(zipPath);

      const archive = archiver('zip', {
        zlib: { level: 9 }, // Sets the compression level.
      });

      archive.on('warning', async function (err) {
        if (fs.existsSync(`assets/${folder_name}`)) {
          await this.removeFile(zipPath);
          await this.removeDir(folderPath, { recursive: true });
        }

        if (err.code === 'ENOENT') {
          // log warning
          console.log('archive warning:', err);
        } else {
          // throw error
          throw new Error('Something went wrong please try again later');
        }
      });

      archive.on('error', async function (err) {
        if (fs.existsSync(`assets/${folder_name}`)) {
          this.removeFile(zipPath);
          this.removeDir(folderPath, { recursive: true });
        }
        throw new Error('Something went wrong please try again later');
      });

      archive.pipe(zipOutPut);

      for (const { pdfBuffer, fileName } of pdfBuffers) {
        archive.append(pdfBuffer, { name: fileName });
      }

      archive.finalize();

      zipOutPut.on('close', async () => {
        try {
          const attachment = fs.readFileSync(zipPath);

          const mail = {
            to: body.email,
            cc: body.cc_emails ? body.cc_emails : [],
            subject: 'Client Details Information',
            from: '',
            text: 'Hai',
            html: `<html>
                    <head>
                    </head>
                    <body>
                      <p> Dear ${body?.email},</p>
                      <br/>
                      <p>We have shared the necessary information regarding your recent request. </p>
                      <br/>
                      <p>Please find the client details attached to this email in a ZIP file. Kindly review the enclosed information carefully. </p>
                      <br/>
                      <p>If you have any questions or need further assistance, please contact us. </p>
                      <br/>
                      <p><b>Thank you.</b></p>
                    </body>
                  </html>`,
            attachments: [
              {
                content: Buffer.from(attachment).toString('base64'),
                filename: `${folder_name}.zip`,
                type: mimetype.lookup(`${folder_name}.zip`),
                disposition: 'attachment',
              },
            ],
          };

          if (fs.existsSync(folderPath)) {
            this.removeDir(folderPath, { recursive: true });
          }

          await this.sendGridService.sendWithOrganizationId(
            organization_id,
            mail,
          );

          this.removeFile(zipPath);

          return response.status(200).json({
            status: true,
            message: 'Client forms summary mail has been sent successfully',
          });
        } catch (error) {
          console.log('Mail Catch error');
          console.log('=========================');
          console.log(error);
          console.log('==========================');

          if (fs.existsSync(zipPath)) {
            this.removeFile(zipPath);
          }

          let errMessage = 'Something went wrong please try again later';

          const errorBody = error?.response?.body;

          if (errorBody?.errors) {
            if (typeof errorBody === 'object') {
              errMessage =
                errorBody?.errors[0]?.message || errorBody?.errors?.messages;
            }
          }

          throw new HttpException(
            errMessage || 'Something went wrong. Please try again later.',
            error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      });
    } catch (error) {
      throw new HttpException(
        error?.message || 'Something went wrong. Please try again later.',
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  removeFile(path: string) {
    fs.unlinkSync(path);
  }

  removeDir(path: string, options?: any) {
    fs.rmSync(path, options);
  }

  async eSignRequest(body: any) {
    try {
      if (!body?.email || !body?.forms) {
        throw new Error('Email and Forms are required');
      }

      const client_details = await this.clientsRepositoryService.findOne({
        client_id: body.client_id,
      });

      if (!client_details) {
        throw new NotFoundException('Client details not found');
      }

      const app_details = await this.appsService.findOne({
        app_code: body.app_code,
      });

      delete body.app_code;

      body.app_id = app_details.app_id;

      body.expiry_timestamp = moment()
        .add(body?.link_expire_in, 'minutes')
        .unix();

      const e_sign_request_details =
        await this.eSignRequestLogsRepository.create({
          ...body,
          e_sign_request_logs_id: generateUUID(),
        });

      if (!e_sign_request_details) {
        throw new NotFoundException(
          'Something went wrong while creating the sign request',
        );
      }

      const link = `${process.env.WEB_APP_LINK}?request_id=${e_sign_request_details.e_sign_request_logs_id}`;

      let organization = await this.orgService.findOne(
        { organization_id: body.organization_id },
        false,
        { id: true, name: true, email: true },
      );

      const { name: organizationName, email: organizationEmail } = organization;

      delete organization?.name;
      delete organization?.email;

      const emailConfigDetails: any =
        await this.ConfigurationsRepository.findOne({
          where: {
            organization,
            type: 'email',
          },
        });

      if (emailConfigDetails && emailConfigDetails.name == 'send-grid') {
        const mail = {
          to: body.email,
          subject: 'E-Sign Request',
          from: emailConfigDetails.details.fromEmail,
          text: 'Complete your e-sign using this link',
          html: `<html>
                  <head>
                  </head>
                  <body>
                    <p>Thank you for choosing ${organizationName.toUpperCase()} for your home care needs. We are dedicated to provide  you with the highest quality of care and ensuring a seamless experience. </p>
                    <br/>
                    <p>Please complete the online signature by following the link below. This step is essential for us to confirm the details of your care plan and ensure that all your needs are met. </p>
                    <br/>
                    <a href="${link}">Online Signature Link</a>
                    <br/>
                    <p>Please note that this link will expire in ${
                      body?.link_expire_in
                    } minutes, so we kindly ask you to complete the online signature  at your earliest convenience. </p>
                    <br/>
                    <p>If you encounter any issues or have questions, please reach out to your organization's Admin or contact our support team at <a href="mailto:${organizationEmail}">${organizationEmail}</a> </p>
                    <br/>
                    <p>Thank you for your cooperation. </p>
                  </body>
                </html>`,
        };

        return (await this.sendGridService.send(
          mail,
          emailConfigDetails.details.apiKey,
        ))
          ? {
              status: true,
              message: 'E-Sign request sent successfully',
            }
          : {
              status: false,
              message: 'Something went wrong. Please try again later',
            };
      } else {
        throw new NotFoundException('Email configuration not found');
      }
    } catch (error) {
      return {
        status: false,
        message:
          error?.message || 'Something went wrong. Please try again later',
      };
    }
  }

  calculateTotalScoreAndPoints = (fields, userAnswers) => {
    let totalPoints = 0;
    let scoredPoints = 0;

    for (const sectionKey in fields) {
      const section = fields[sectionKey];
      const { sectionTotalPoints, sectionScoredPoints } =
        this.calculateSectionScoreAndPoints(
          section.fields,
          userAnswers[sectionKey],
        );

      totalPoints += sectionTotalPoints;
      scoredPoints += sectionScoredPoints;
    }

    return { totalPoints, scoredPoints };
  };

  calculateSectionScoreAndPoints = (sectionFields, userAnswers) => {
    let sectionTotalPoints = 0;
    let sectionScoredPoints = 0;

    sectionFields.forEach((field) => {
      if (field.is_quiz_field) {
        const fieldPoints = field?.points || 0;
        sectionTotalPoints += fieldPoints;

        const correctOptions = field.options.filter((option) => option.status);
        const userResponse = userAnswers[field.name] || [];

        if (field.input_type === 'checkbox') {
          const correctValues = correctOptions.map((opt) => opt?.value);

          const limitType = field.checkbox_answer_type;
          const limitValue = parseInt(field.checkbox_answer_limit, 10) || 0;

          let isCorrect = false;

          if (limitType === 'Equal to') {
            const userCorrectSelections = userResponse.filter((response) =>
              correctValues.includes(response),
            );

            // Give points if at least the limitValue number of correct answers are selected
            isCorrect =
              userCorrectSelections.length >= limitValue &&
              userResponse.length <= limitValue;
          } else {
            // Existing logic for 'No limit' and 'At most'
            isCorrect =
              correctValues &&
              correctValues.every((value) => userResponse.includes(value)) &&
              userResponse.length === correctValues.length;
          }

          if (isCorrect) {
            sectionScoredPoints += fieldPoints;
          }
        } else if (field.input_type === 'radio') {
          const correctOption = correctOptions[0];
          if (userResponse === correctOption?.value) {
            sectionScoredPoints += fieldPoints;
          }
        }
      }
    });

    return {
      sectionTotalPoints,
      sectionScoredPoints,
    };
  };

  /**
   * Update Field
   * @param organization string
   * @param form_id string
   * @param body UpdaeField
   * @returns Promise<FIELD>
   */
  async customValidation(
    organization: string,
    form_id: string,
    body: CustomValidationBody,
  ): Promise<FIELD> {
    try {
      const group_key = body.group_key;
      const field_id = body.field_id;
      const key = `fields.${group_key}.fields`;
      const form = await this.findFieldInFormWithIds(
        organization,
        form_id,
        field_id,
        group_key,
      );
      if (!form)
        throw {
          status: HttpStatus.NOT_FOUND,
          message: 'Form/Field Not Found',
        };
      let updated_field: FIELD;
      const fields = form.fields[group_key].fields.map((field: FIELD) => {
        if (field.field_id === field_id) {
          updated_field = {
            ...field,
            ...body,
          };
          return updated_field;
        } else {
          return field;
        }
      });
      const validationschema = this.globalService.generateValidationSchema(
        form,
        group_key,
        updated_field,
      );
      const data = await this.OrgFormsRepository.findOneAndUpdate(
        { form_id },
        {
          [key]: fields,
          validationschema,
        },
        {
          new: true,
        },
      )
        .select(this.select)
        .catch((error) => {
          throw error;
        });
      if (body.validation_schema.conditional_validation) {
        const condDependencies = {
          selectedFormId: form_id,
          selectedGroupKey: group_key,
          selectedInputId: field_id,
          isConditionsRequired:
            body.validation_schema.conditional_validation.isConditionsRequired,
          isDisplayed:
            body.validation_schema.conditional_validation.isDisplayed,
        };

        const result = await this.OrgFormsRepository.aggregate([
          {
            $match: {
              form_id:
                body.validation_schema.conditional_validation.selectedFormId,
            },
          },
          {
            $project: {
              [`field`]: {
                $filter: {
                  input: `$fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields`,
                  as: 'f',
                  cond: {
                    $eq: [
                      '$$f.field_id',
                      body.validation_schema.conditional_validation
                        .selectedInputId,
                    ],
                  },
                },
              },
            },
          },
        ]);
        const matchingField = result[0]?.field[0];
        matchingField['dependencies'] = [];
        if (
          matchingField['dependencies'] &&
          matchingField['dependencies'].length > 0
        ) {
          const existedField = matchingField['dependencies'].find(
            (f) => f.field_id === field_id,
          );
          if (!existedField) {
            matchingField['dependencies'].push(condDependencies);
          }
        } else {
          matchingField['dependencies'].push(condDependencies);
        }

        const update = await this.OrgFormsRepository.findOneAndUpdate(
          {
            form_id:
              body.validation_schema.conditional_validation.selectedFormId,
            [`fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields.field_id`]:
              body.validation_schema.conditional_validation.selectedInputId,
          },
          {
            $set: {
              [`fields.${body.validation_schema.conditional_validation.selectedGroupKey}.fields.$[elem]`]:
                {
                  ...matchingField,
                },
            },
          },
          {
            arrayFilters: [
              {
                'elem.field_id':
                  body.validation_schema.conditional_validation.selectedInputId,
              },
            ],
            new: true,
          },
        );
      }
      if (data) return updated_field;
      throw {
        status: HttpStatus.CONFLICT,
        message: 'Unable to update the field',
      };
    } catch (error) {
      throw error;
    }
  }
}
