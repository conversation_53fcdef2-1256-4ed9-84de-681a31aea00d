# Job Module Implementation

## Overview
This document describes the implementation of the Job module that has been integrated with the OnboardingEmployee system. The Job module allows organizations to create and manage job positions, and employees can be assigned to specific jobs during the onboarding process.

## Features

### Job Management
- Create, read, update, and delete job positions
- Job positions are organization-specific
- Support for various employment types (Full-time, Part-time, Contract, Temporary, Internship)
- Job status management (Active, Inactive, Draft)

### Job Fields
- **job_id**: Unique identifier for each job
- **title**: Job title (required)
- **department**: Department name (optional)
- **description**: Detailed job description (optional)
- **requirements**: Job requirements (optional)
- **salary_range**: Salary range information (optional)
- **employment_type**: Type of employment (enum)
- **location**: Job location (optional)
- **status**: Job status (enum)
- **organization**: Associated organization (required)

### Integration with OnboardingEmployee
- OnboardingEmployee can now be associated with a Job
- Job selection is optional during employee creation
- Job information is included in employee details

## API Endpoints

### Job Management
- `POST /job` - Create a new job
- `GET /job` - Get all jobs for the organization
- `GET /job/active` - Get only active jobs for the organization
- `GET /job/:job_id` - Get specific job details
- `PUT /job/:job_id` - Update job details
- `DELETE /job/:job_id` - Delete a job (soft delete)

### OnboardingEmployee with Job
- `POST /onboarding-employee` - Create employee with optional job_id
- `PUT /onboarding-employee/:id` - Update employee with optional job_id
- `GET /onboarding-employee` - List employees with job information
- `GET /onboarding-employee/:id` - Get employee details with job information

## Request/Response Examples

### Create Job
```json
POST /job
{
  "title": "Software Engineer",
  "department": "Engineering",
  "description": "Develop and maintain software applications",
  "requirements": "Bachelor's degree in Computer Science, 3+ years experience",
  "salary_range": "$80,000 - $120,000",
  "employment_type": "Full-time",
  "location": "Remote",
  "status": "Active"
}
```

### Create OnboardingEmployee with Job
```json
POST /onboarding-employee
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "mobile_number": "+1234567890",
  "password": "securepassword",
  "job_id": "job-uuid-here"
}
```

## Database Schema

### Job Entity
```sql
CREATE TABLE job (
  id INT PRIMARY KEY AUTO_INCREMENT,
  job_id VARCHAR(255) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  department VARCHAR(255),
  description TEXT,
  requirements TEXT,
  salary_range VARCHAR(255),
  employment_type ENUM('Full-time', 'Part-time', 'Contract', 'Temporary', 'Internship') DEFAULT 'Full-time',
  location VARCHAR(255),
  status ENUM('Active', 'Inactive', 'Draft') DEFAULT 'Active',
  organization_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  FOREIGN KEY (organization_id) REFERENCES organization(id)
);
```

### OnboardingEmployee Update
```sql
ALTER TABLE onboarding_employee 
ADD COLUMN job_id INT NULL,
ADD FOREIGN KEY (job_id) REFERENCES job(id);
```

## Security
- All job endpoints require JWT authentication
- Organization-level authorization ensures users can only access jobs within their organization
- Job assignment validation ensures jobs belong to the same organization as the employee

## Error Handling
- Proper HTTP status codes for different scenarios
- Validation errors for required fields
- Not found errors for invalid job_id references
- Organization mismatch errors for cross-organization access attempts

## Usage Notes
1. Jobs must be created before they can be assigned to employees
2. Job assignment is optional - employees can be created without a job
3. Only active jobs should typically be assigned to new employees
4. Job information is included in employee responses for better context
5. Deleting a job performs a soft delete to maintain data integrity

## Files Modified/Created
- `src/entities/job.entity.ts` - Job entity definition
- `src/dto/job.dto.ts` - Job DTOs for validation
- `src/services/job.service.ts` - Job business logic
- `src/controllers/job/job.controller.ts` - Job API endpoints
- `src/controllers/job/job.module.ts` - Job module configuration
- `src/entities/onboarding-employee.entity.ts` - Added job relationship
- `src/dto/onboarding-employee.dto.ts` - Added job_id field
- `src/services/onboarding-employee.service.ts` - Added job handling
- `src/controllers/employee/onboarding-employee.module.ts` - Added job dependencies
- `src/util/modules/database.module.ts` - Added job entity
- `src/app.module.ts` - Added job module
