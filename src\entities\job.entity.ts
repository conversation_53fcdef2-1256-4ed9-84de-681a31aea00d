import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';

import { Organization } from './organization.entity';
import { OnboardingEmployee } from './onboarding-employee.entity';

export enum EMPLOYMENT_TYPE {
  FULL_TIME = 'Full-time',
  PART_TIME = 'Part-time',
  CONTRACT = 'Contract',
  TEMPORARY = 'Temporary',
  INTERNSHIP = 'Internship',
}

export enum JOB_STATUS {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
  DRAFT = 'Draft',
}

@Entity()
export class Job {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  job_id: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  department: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  requirements: string;

  @Column({ nullable: true })
  salary_range: string;

  @Column({
    type: 'enum',
    enum: EMPLOYMENT_TYPE,
    default: EMPLOYMENT_TYPE.FULL_TIME,
  })
  employment_type: EMPLOYMENT_TYPE;

  @Column({ nullable: true })
  location: string;

  @Column({
    type: 'enum',
    enum: JOB_STATUS,
    default: JOB_STATUS.ACTIVE,
  })
  status: JOB_STATUS;

  @ManyToOne(
    () => Organization,
    (organization: Organization) => organization.id,
    { eager: false, nullable: false },
  )
  @JoinColumn()
  organization: Organization;

  @OneToMany(
    () => OnboardingEmployee,
    (onboardingEmployee: OnboardingEmployee) => onboardingEmployee.job,
  )
  onboarding_employees: OnboardingEmployee[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
