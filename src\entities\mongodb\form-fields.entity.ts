import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  HydratedDocument,
  ObjectId,
  SchemaOptions,
  SchemaTypes,
} from 'mongoose';
import { GROUP } from 'src/util/interfaces/forms.interface';

const FormFieldsSchemaOptions: SchemaOptions = {
  timestamps: true,
  collection: 'form_fields',
  versionKey: false,
};

@Schema(FormFieldsSchemaOptions)
export class FormFields {
  @Prop({ type: SchemaTypes.ObjectId, name: '_id' })
  id: ObjectId;

  @Prop({ unique: true, type: SchemaTypes.String, required: true })
  form_fields_id: string;

  @Prop({ type: SchemaTypes.String, required: true, index: true })
  form_id: string;

  @Prop({ type: SchemaTypes.String, required: true })
  section_key: string;

  @Prop({ type: SchemaTypes.String, required: true })
  section_name: string;

  @Prop({ type: SchemaTypes.Number, default: 0 })
  section_index: number;

  @Prop({ type: SchemaTypes.Mixed })
  section_data: GROUP;

  @Prop({ type: SchemaTypes.Date, default: null })
  deleted_at: Date;
}

export type FormFieldsDocument = HydratedDocument<FormFields>;
export const FormFieldsSchema = SchemaFactory.createForClass(FormFields);
