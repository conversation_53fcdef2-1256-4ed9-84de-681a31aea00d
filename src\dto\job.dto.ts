import {
  <PERSON>Enum,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  IsDateString,
} from 'class-validator';
import { EMPLOYMENT_TYPE, JOB_STATUS } from 'src/entities/job.entity';

export class CreateJobDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  department: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  requirements: string;

  @IsOptional()
  @IsString()
  salary_range: string;

  @IsOptional()
  @IsEnum(EMPLOYMENT_TYPE)
  employment_type: EMPLOYMENT_TYPE;

  @IsOptional()
  @IsString()
  location: string;

  @IsOptional()
  @IsEnum(JOB_STATUS)
  status: JOB_STATUS;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;
}

export class UpdateJobDto {
  @IsOptional()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  department: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsOptional()
  @IsString()
  requirements: string;

  @IsOptional()
  @IsString()
  salary_range: string;

  @IsOptional()
  @IsEnum(EMPLOYMENT_TYPE)
  employment_type: EMPLOYMENT_TYPE;

  @IsOptional()
  @IsString()
  location: string;

  @IsOptional()
  @IsEnum(JOB_STATUS)
  status: JOB_STATUS;

  @IsOptional()
  @IsDateString()
  application_start_date: string;

  @IsOptional()
  @IsDateString()
  application_end_date: string;

  @IsOptional()
  @IsString()
  change_reason: string;
}
